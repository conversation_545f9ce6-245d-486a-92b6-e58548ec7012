# Recurrent Spiking Neural Network (RSNN) with Biological Dynamics

A comprehensive PyTorch implementation of a recurrent spiking neural network that simulates biological brain dynamics with continuous activity and advanced learning mechanisms.

## 🧠 Key Features

### Core Architecture
- **Recurrent Connections**: Non-feedforward architecture with loops between neurons
- **Continuous Activity**: Neural activity persists indefinitely once initiated
- **Spiking Neurons**: Temporal dynamics using Leaky Integrate-and-Fire (LIF) neurons
- **Refractory Periods**: Biologically realistic post-spike recovery periods

### Advanced Biological Features

#### 1. Synaptogenesis Pruning Algorithm (SPA)
- **Dynamic Connection Formation**: Creates new synaptic connections during learning
- **Adaptive Pruning**: Removes weak or unused connections over time
- **Homeostatic Regulation**: Maintains optimal connectivity levels
- **Activity-Dependent Plasticity**: Connection strength adapts based on neural activity

#### 2. Spike Timing Dependent Plasticity (STDP)
- **Temporal Learning**: Synaptic strength depends on precise spike timing
- **LTP/LTD Mechanisms**: Long-term potentiation and depression
- **Asymmetric Learning Window**: Pre-before-post strengthens, post-before-pre weakens
- **Trace-Based Implementation**: Exponential decay traces for temporal correlation

#### 3. Memory Formation Through Loops
- **Reverberating Activity**: Short-term memory via sustained neural loops
- **Pattern Storage**: Store and recall complex activity patterns
- **Multiple Memory Loops**: Independent memory circuits for different patterns
- **Inter-Loop Interactions**: Memory networks with cross-loop communication

#### 4. Temporal Dynamics
- **Spike Timing Precision**: Information encoded in precise spike times
- **Temporal Pattern Recognition**: Process and learn temporal sequences
- **Continuous Time Simulation**: Real-time neural dynamics
- **Adaptive Neurons**: Spike-frequency adaptation for realistic responses

## 📁 Project Structure

```
├── spiking_neuron.py          # LIF neuron implementations
├── synaptogenesis_pruning.py  # SPA algorithm
├── spike_timing.py            # STDP and temporal coding
├── memory_dynamics.py         # Memory loops and networks
├── recurrent_snn.py          # Main network implementation
├── visualization.py          # Comprehensive visualization tools
├── test_snn.py              # Complete test suite
├── examples.py              # Usage examples and demonstrations
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```python
import torch
from recurrent_snn import RecurrentSpikingNeuralNetwork

# Create network
network = RecurrentSpikingNeuralNetwork(
    num_neurons=50,
    input_size=10,
    output_size=5,
    enable_spa=True,      # Enable Synaptogenesis Pruning
    enable_stdp=True,     # Enable STDP learning
    enable_memory=True,   # Enable memory loops
    device='cpu'
)

# Create input data
input_data = torch.rand(1, 10)  # [batch_size, input_size]

# Run simulation
results = network(
    input_data, 
    num_steps=100, 
    learning_rate=0.01,
    return_traces=True
)

print(f"Output shape: {results['output'].shape}")
print(f"Total spikes: {results['total_spikes']}")
```

### Continuous Activity Simulation

```python
# Enable continuous background activity
network.enable_continuous_activity(
    background_current=0.1,
    noise_level=0.05
)

# Run continuous simulation
results = network.run_continuous_simulation(
    duration=1000,
    input_patterns=[torch.rand(1, 10) * 0.5],
    pattern_intervals=[100],
    learning_rate=0.01
)
```

### Memory Storage and Recall

```python
# Store a pattern in memory
pattern = torch.rand(1, 50)
pattern_id = network.store_memory_pattern(pattern, "my_pattern")

# Recall the pattern later
recalled_pattern = network.recall_memory_pattern(pattern_id)
```

## 🧪 Running Tests

```bash
# Run comprehensive test suite
python test_snn.py
```

The test suite includes:
- Individual component tests (neurons, SPA, STDP, memory)
- Integration tests for the complete network
- Non-linear behavior demonstrations
- Temporal pattern recognition tests
- Memory formation and recall validation

## 📊 Examples and Demonstrations

```bash
# Run all examples
python examples.py
```

### Available Examples:

1. **Basic Usage**: Fundamental network operations and spike generation
2. **Continuous Activity**: Persistent neural activity and memory formation
3. **Learning Dynamics**: SPA and STDP learning over time
4. **Temporal Patterns**: Processing and recognition of temporal sequences
5. **Memory Recall**: Storage and retrieval of complex patterns

## 🔬 Key Algorithms

### Synaptogenesis Pruning Algorithm (SPA)

The SPA algorithm dynamically manages synaptic connections:

1. **Growth Phase**: Forms new connections based on correlated activity
2. **Pruning Phase**: Removes connections below activity threshold
3. **Homeostatic Regulation**: Maintains target connectivity levels
4. **Activity Monitoring**: Tracks connection usage over time windows

### Spike Timing Dependent Plasticity (STDP)

STDP implements temporal learning rules:

- **LTP Window**: Pre-synaptic spike followed by post-synaptic spike strengthens connection
- **LTD Window**: Post-synaptic spike followed by pre-synaptic spike weakens connection
- **Exponential Decay**: Learning window decreases exponentially with time difference
- **Weight Bounds**: Synaptic weights are bounded to prevent runaway dynamics

### Memory Loop Dynamics

Memory formation through reverberating activity:

- **Loop Activation**: Input patterns activate specific neural loops
- **Sustained Activity**: Recurrent connections maintain activity
- **Decay and Noise**: Gradual decay with stochastic fluctuations
- **Pattern Recall**: Stored patterns can be reactivated

## 📈 Visualization

The visualization module provides comprehensive analysis tools:

```python
from visualization import SNNVisualizer

visualizer = SNNVisualizer()

# Spike raster plots
visualizer.plot_spike_raster(spike_data, title="Network Activity")

# Membrane potential traces
visualizer.plot_membrane_potential(membrane_data, neuron_indices=[0,1,2])

# Connectivity matrices
visualizer.plot_connectivity_matrix(connections, weights)

# Learning dynamics
visualizer.plot_learning_dynamics(spa_stats, stdp_stats)

# Memory network state
visualizer.plot_memory_dynamics(memory_stats, memory_activity)
```

## 🎯 Applications

This implementation is suitable for:

- **Neuromorphic Computing**: Brain-inspired computing architectures
- **Temporal Pattern Recognition**: Processing time-series data
- **Associative Memory**: Content-addressable memory systems
- **Adaptive Control**: Self-organizing control systems
- **Neuroscience Research**: Modeling biological neural networks
- **Edge Computing**: Low-power, event-driven processing

## 🔧 Configuration Options

### Network Parameters
- `num_neurons`: Number of neurons in the network
- `input_size`: Dimensionality of input data
- `output_size`: Dimensionality of output
- `neuron_type`: 'basic' or 'adaptive' neurons
- `initial_connectivity`: Starting connection probability

### Learning Parameters
- `enable_spa`: Enable/disable Synaptogenesis Pruning Algorithm
- `enable_stdp`: Enable/disable STDP learning
- `enable_memory`: Enable/disable memory loops
- `learning_rate`: Rate of synaptic weight updates

### Neuron Parameters
- `threshold`: Spike generation threshold
- `refractory_period`: Post-spike recovery time
- `membrane_decay`: Membrane potential decay rate
- `adaptation_strength`: Spike-frequency adaptation strength

## 📚 Scientific Background

This implementation is based on established neuroscience principles:

1. **Leaky Integrate-and-Fire Neurons**: Standard computational neuron model
2. **STDP Learning**: Discovered by Bi & Poo (1998), fundamental synaptic plasticity rule
3. **Synaptogenesis**: Biological process of synapse formation and elimination
4. **Reverberating Circuits**: Neural basis of working memory (Hebb, 1949)
5. **Homeostatic Plasticity**: Maintains network stability during learning

## 🤝 Contributing

Contributions are welcome! Areas for improvement:

- Additional neuron models (Izhikevich, Hodgkin-Huxley)
- More sophisticated learning rules
- GPU acceleration optimizations
- Additional visualization features
- Performance benchmarks

## 📄 License

This project is open source. Please cite this work if used in research.

## 🔗 References

- Bi, G., & Poo, M. (1998). Synaptic modifications in cultured hippocampal neurons
- Hebb, D. O. (1949). The Organization of Behavior
- Izhikevich, E. M. (2003). Simple model of spiking neurons
- Song, S., Miller, K. D., & Abbott, L. F. (2000). Competitive Hebbian learning

---

**Note**: This implementation prioritizes biological realism while maintaining computational efficiency. The network demonstrates emergent properties characteristic of biological neural networks, including persistent activity, adaptive connectivity, and memory formation.
