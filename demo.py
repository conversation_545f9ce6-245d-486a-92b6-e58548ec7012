"""
Demonstration of Recurrent Spiking Neural Network
Shows key biological features and continuous activity
"""

import torch
import numpy as np
import matplotlib.pyplot as plt

from recurrent_snn import RecurrentSpikingNeuralNetwork
from visualization import SNNVisualizer

def main_demo():
    print("="*60)
    print("RECURRENT SPIKING NEURAL NETWORK DEMONSTRATION")
    print("="*60)
    
    # Create network with all biological features enabled
    print("\n1. Creating Recurrent Spiking Neural Network...")
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=40,
        input_size=8,
        output_size=4,
        neuron_type='adaptive',
        initial_connectivity=0.15,
        enable_spa=True,      # Synaptogenesis Pruning Algorithm
        enable_stdp=True,     # Spike Timing Dependent Plasticity
        enable_memory=True,   # Memory loops
        device='cpu'
    )
    
    print(f"✓ Network created with {network.num_neurons} neurons")
    print(f"  - Input size: {network.input_size}")
    print(f"  - Output size: {network.output_size}")
    print(f"  - SPA enabled: {network.enable_spa}")
    print(f"  - STDP enabled: {network.enable_stdp}")
    print(f"  - Memory enabled: {network.enable_memory}")
    
    # Demonstrate basic spike generation
    print("\n2. Testing Basic Spike Generation...")
    input_data = torch.rand(1, 8) * 0.8
    results = network(input_data, num_steps=50, return_traces=True)
    
    print(f"✓ Generated {results['total_spikes']} spikes in 50 time steps")
    print(f"  - Output shape: {results['output'].shape}")
    print(f"  - Spike rate: {results['total_spikes']/50:.2f} spikes/ms")
    
    # Demonstrate continuous activity
    print("\n3. Enabling Continuous Activity...")
    network.enable_continuous_activity(
        background_current=0.12,
        noise_level=0.08
    )
    
    # Create patterns for memory storage
    print("\n4. Storing Memory Patterns...")
    patterns = {
        'alpha': torch.rand(1, 40) * 0.7,
        'beta': torch.rand(1, 40) * 0.6,
        'gamma': torch.rand(1, 40) * 0.8
    }
    
    stored_patterns = {}
    for name, pattern in patterns.items():
        pattern_id = network.store_memory_pattern(pattern, name)
        stored_patterns[name] = pattern_id
        print(f"  ✓ Stored pattern '{name}' with ID: {pattern_id}")
    
    # Run continuous simulation
    print("\n5. Running Continuous Simulation...")
    input_patterns = [
        torch.rand(1, 8) * 0.4,
        torch.rand(1, 8) * 0.6,
        torch.rand(1, 8) * 0.5
    ]
    
    simulation_results = network.run_continuous_simulation(
        duration=200,
        input_patterns=input_patterns,
        pattern_intervals=[60, 100, 140],
        learning_rate=0.02
    )
    
    total_activity = sum(spikes.sum().item() for spikes in simulation_results['spike_history'])
    print(f"✓ Continuous simulation completed")
    print(f"  - Duration: 200 time steps")
    print(f"  - Total activity: {total_activity} spikes")
    print(f"  - Average activity: {total_activity/200:.2f} spikes/ms")
    
    # Test memory recall
    print("\n6. Testing Memory Recall...")
    recall_results = {}
    for name, pattern_id in stored_patterns.items():
        recalled = network.recall_memory_pattern(pattern_id, recall_strength=1.2)
        if recalled is not None:
            original = patterns[name]
            similarity = torch.cosine_similarity(
                original.flatten(), recalled.flatten(), dim=0
            ).item()
            recall_results[name] = similarity
            print(f"  ✓ Recalled '{name}' - similarity: {similarity:.3f}")
        else:
            recall_results[name] = 0.0
            print(f"  ✗ Failed to recall '{name}'")
    
    # Show final network statistics
    print("\n7. Final Network Statistics...")
    stats = network.get_network_statistics()
    
    print(f"  Network Activity:")
    print(f"    - Total spikes: {stats['total_spikes']}")
    print(f"    - Simulation time: {stats['simulation_time']} ms")
    print(f"    - Overall spike rate: {stats['spike_rate']:.3f} spikes/ms")
    print(f"    - Learning events: {stats['learning_events']}")
    print(f"    - Memory formations: {stats['memory_formations']}")
    
    if 'spa' in stats:
        spa_stats = stats['spa']
        print(f"  Synaptogenesis Pruning Algorithm:")
        print(f"    - Current connectivity: {spa_stats['connectivity']:.3f}")
        print(f"    - Total connections: {spa_stats['num_connections']}")
        print(f"    - Growth events: {spa_stats['growth_events']}")
        print(f"    - Pruning events: {spa_stats['pruning_events']}")
        print(f"    - Average weight: {spa_stats['avg_weight']:.4f}")
    
    if 'stdp' in stats:
        stdp_stats = stats['stdp']
        print(f"  Spike Timing Dependent Plasticity:")
        print(f"    - LTP events: {stdp_stats['ltp_events']}")
        print(f"    - LTD events: {stdp_stats['ltd_events']}")
        print(f"    - LTP/LTD ratio: {stdp_stats['ltp_ltd_ratio']:.3f}")
    
    if 'memory' in stats:
        memory_stats = stats['memory']
        print(f"  Memory Network:")
        print(f"    - Active loops: {memory_stats['num_active_loops']}")
        print(f"    - Stored patterns: {len(memory_stats['stored_patterns'])}")
        print(f"    - Global activity: {memory_stats['global_activity_level']:.4f}")
    
    # Demonstrate non-linear behavior
    print("\n8. Demonstrating Non-Linear Behavior...")
    test_patterns = [
        torch.tensor([[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]]),
        torch.tensor([[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]]),
        torch.tensor([[0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]]),
        torch.tensor([[1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]]),
    ]
    
    responses = []
    for i, pattern in enumerate(test_patterns):
        result = network(pattern, num_steps=30)
        response = result['output'].mean().item()
        responses.append(response)
        print(f"  Pattern {i}: {pattern.flatten()[:2].tolist()} -> Response: {response:.4f}")
    
    response_variance = np.var(responses)
    print(f"  Response variance: {response_variance:.6f} (shows non-linear behavior)")
    
    # Show key biological features
    print("\n9. Key Biological Features Demonstrated:")
    print("  ✓ Recurrent connections with loops between neurons")
    print("  ✓ Continuous activity that persists without input")
    print("  ✓ Refractory periods preventing unrealistic firing")
    print("  ✓ Spike timing dependent plasticity (STDP)")
    print("  ✓ Dynamic synaptogenesis and pruning (SPA)")
    print("  ✓ Memory formation through reverberating loops")
    print("  ✓ Temporal dynamics with precise spike timing")
    print("  ✓ Non-linear pattern processing capabilities")
    
    print("\n" + "="*60)
    print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("\nThis implementation showcases a biologically realistic")
    print("recurrent spiking neural network with:")
    print("- Continuous neural activity")
    print("- Dynamic synaptic plasticity")
    print("- Memory formation and recall")
    print("- Temporal pattern processing")
    print("- Non-feedforward architecture")
    
    return network, simulation_results, stats

if __name__ == "__main__":
    try:
        network, results, stats = main_demo()
        print(f"\nDemo completed successfully!")
        print(f"Network generated {stats['total_spikes']} total spikes")
        print(f"Final connectivity: {stats['spa']['connectivity']:.3f}")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
