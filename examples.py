"""
Example Usage of Recurrent Spiking Neural Network
Demonstrates key features and biological dynamics
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from recurrent_snn import RecurrentSpikingNeuralNetwork
from visualization import SNNVisualizer


def example_1_basic_usage():
    """Example 1: Basic network usage and spike generation"""
    print("\n" + "="*50)
    print("EXAMPLE 1: Basic Network Usage")
    print("="*50)
    
    # Create network
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=30,
        input_size=5,
        output_size=3,
        device='cpu'
    )
    
    # Create input data
    input_data = torch.rand(1, 5) * 0.8  # [batch_size=1, input_size=5]
    
    # Run simulation
    results = network(input_data, num_steps=100, return_traces=True)
    
    print(f"Network output shape: {results['output'].shape}")
    print(f"Total spikes generated: {results['total_spikes']}")
    print(f"Spike traces shape: {results['spike_traces'].shape}")
    
    # Visualize results
    visualizer = SNNVisualizer()
    
    # Plot spike raster
    visualizer.plot_spike_raster(
        results['spike_traces'][0],  # Remove batch dimension
        title="Example 1: Basic Spike Raster",
        neuron_subset=list(range(15))  # Show first 15 neurons
    )
    
    # Plot membrane potentials
    visualizer.plot_membrane_potential(
        results['membrane_traces'][0],
        neuron_indices=[0, 1, 2, 3, 4],
        title="Example 1: Membrane Potential Traces"
    )
    
    return network, results


def example_2_continuous_activity():
    """Example 2: Continuous activity and memory formation"""
    print("\n" + "="*50)
    print("EXAMPLE 2: Continuous Activity & Memory")
    print("="*50)
    
    # Create network with memory enabled
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=40,
        input_size=6,
        output_size=4,
        enable_memory=True,
        device='cpu'
    )
    
    # Enable continuous background activity
    network.enable_continuous_activity(
        background_current=0.12,
        noise_level=0.06
    )
    
    # Create memory patterns
    pattern1 = torch.rand(1, 40) * 0.7
    pattern2 = torch.rand(1, 40) * 0.8
    
    # Store patterns in memory
    pid1 = network.store_memory_pattern(pattern1, "memory_pattern_1")
    pid2 = network.store_memory_pattern(pattern2, "memory_pattern_2")
    
    print(f"Stored memory patterns: {pid1}, {pid2}")
    
    # Create input patterns for injection
    input_patterns = [
        torch.rand(1, 6) * 0.4,
        torch.rand(1, 6) * 0.6,
        torch.rand(1, 6) * 0.5
    ]
    
    # Run continuous simulation
    print("Running continuous simulation...")
    results = network.run_continuous_simulation(
        duration=300,
        input_patterns=input_patterns,
        pattern_intervals=[80, 120, 160],
        learning_rate=0.015
    )
    
    # Analyze results
    total_activity = sum(spikes.sum().item() for spikes in results['spike_history'])
    print(f"Total network activity: {total_activity} spikes")
    
    # Show connectivity evolution
    if results['connectivity_history']:
        times = [h['time'] for h in results['connectivity_history']]
        connectivity = [h['connectivity'] for h in results['connectivity_history']]
        
        plt.figure(figsize=(10, 6))
        plt.plot(times, connectivity, 'b-', linewidth=2)
        plt.xlabel('Time (ms)')
        plt.ylabel('Network Connectivity')
        plt.title('Example 2: Connectivity Evolution During Continuous Activity')
        plt.grid(True, alpha=0.3)
        plt.show()
    
    # Show memory activity
    if results['memory_activity']:
        times = [m['time'] for m in results['memory_activity']]
        active_loops = [m['active_loops'] for m in results['memory_activity']]
        
        plt.figure(figsize=(10, 6))
        plt.plot(times, active_loops, 'r-', linewidth=2)
        plt.xlabel('Time (ms)')
        plt.ylabel('Number of Active Memory Loops')
        plt.title('Example 2: Memory Loop Activity Over Time')
        plt.grid(True, alpha=0.3)
        plt.show()
    
    return network, results


def example_3_learning_dynamics():
    """Example 3: Learning dynamics with SPA and STDP"""
    print("\n" + "="*50)
    print("EXAMPLE 3: Learning Dynamics")
    print("="*50)
    
    # Create network with all learning features
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=35,
        input_size=7,
        output_size=3,
        initial_connectivity=0.12,
        enable_spa=True,
        enable_stdp=True,
        device='cpu'
    )
    
    # Training patterns
    training_patterns = [
        torch.rand(1, 7) * 0.6,
        torch.rand(1, 7) * 0.8,
        torch.rand(1, 7) * 0.5,
        torch.rand(1, 7) * 0.9
    ]
    
    print("Training network with multiple patterns...")
    
    # Track learning progress
    connectivity_history = []
    learning_history = []
    
    # Training loop
    for epoch in range(10):
        epoch_spikes = 0
        
        for pattern in training_patterns:
            results = network(pattern, num_steps=50, learning_rate=0.02)
            epoch_spikes += results['total_spikes']
            
        # Record statistics
        stats = network.get_network_statistics()
        connectivity_history.append(stats['spa']['connectivity'])
        learning_history.append(stats['learning_events'])
        
        print(f"Epoch {epoch+1}: Connectivity={stats['spa']['connectivity']:.3f}, "
              f"Learning events={stats['learning_events']}, Spikes={epoch_spikes}")
    
    # Visualize learning progress
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Connectivity evolution
    ax1.plot(range(1, 11), connectivity_history, 'b-o', linewidth=2)
    ax1.set_xlabel('Training Epoch')
    ax1.set_ylabel('Network Connectivity')
    ax1.set_title('Connectivity Evolution During Learning')
    ax1.grid(True, alpha=0.3)
    
    # Learning events
    ax2.plot(range(1, 11), learning_history, 'r-s', linewidth=2)
    ax2.set_xlabel('Training Epoch')
    ax2.set_ylabel('Cumulative Learning Events')
    ax2.set_title('Learning Events Over Time')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Show final network state
    visualizer = SNNVisualizer()
    final_weights = network.get_effective_recurrent_weights()
    final_connections = network.spa.connection_matrix if network.enable_spa else network.connection_matrix
    
    visualizer.plot_connectivity_matrix(
        final_connections,
        final_weights,
        title="Example 3: Final Network Connectivity After Learning"
    )
    
    return network, connectivity_history, learning_history


def example_4_temporal_patterns():
    """Example 4: Temporal pattern processing and recognition"""
    print("\n" + "="*50)
    print("EXAMPLE 4: Temporal Pattern Processing")
    print("="*50)
    
    # Create network optimized for temporal processing
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=25,
        input_size=5,
        output_size=2,
        neuron_type='adaptive',  # Use adaptive neurons
        device='cpu'
    )
    
    # Create distinct temporal patterns
    def create_temporal_pattern(spike_times: List[Tuple[int, int]], duration: int = 50):
        """Create temporal pattern with spikes at specific (time, neuron) coordinates"""
        pattern = torch.zeros(duration, 5)
        for time, neuron in spike_times:
            if time < duration and neuron < 5:
                pattern[time, neuron] = 1.0
        return pattern
    
    # Define temporal patterns
    pattern_A = create_temporal_pattern([(5, 0), (10, 1), (15, 2), (20, 3)])
    pattern_B = create_temporal_pattern([(8, 0), (16, 1), (24, 2), (32, 3)])
    pattern_C = create_temporal_pattern([(3, 1), (12, 0), (21, 3), (30, 2)])
    
    patterns = [pattern_A, pattern_B, pattern_C]
    pattern_names = ['Pattern A', 'Pattern B', 'Pattern C']
    
    print("Testing temporal pattern responses...")
    
    # Test network responses to different patterns
    responses = []
    for i, pattern in enumerate(patterns):
        result = network(pattern.unsqueeze(0), num_steps=50, return_traces=True)
        
        # Calculate response characteristics
        output_activity = result['output'].mean().item()
        spike_timing = result['spike_traces'][0].nonzero()
        
        responses.append({
            'pattern': pattern_names[i],
            'output_activity': output_activity,
            'total_spikes': result['total_spikes'],
            'spike_timing': spike_timing
        })
        
        print(f"{pattern_names[i]}: Output={output_activity:.3f}, "
              f"Spikes={result['total_spikes']}")
    
    # Visualize temporal patterns and responses
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    # Plot input patterns
    for i, pattern in enumerate(patterns):
        spike_times, neurons = pattern.nonzero(as_tuple=True)
        axes[0, i].scatter(spike_times, neurons, s=50, c='red', alpha=0.8)
        axes[0, i].set_title(f'Input {pattern_names[i]}')
        axes[0, i].set_xlabel('Time (ms)')
        axes[0, i].set_ylabel('Input Neuron')
        axes[0, i].set_ylim(-0.5, 4.5)
        axes[0, i].grid(True, alpha=0.3)
    
    # Plot network responses
    for i, response in enumerate(responses):
        # Get network activity for this pattern
        result = network(patterns[i].unsqueeze(0), num_steps=50, return_traces=True)
        network_spikes = result['spike_traces'][0]
        
        spike_times, neurons = network_spikes.nonzero(as_tuple=True)
        axes[1, i].scatter(spike_times, neurons, s=10, c='blue', alpha=0.6)
        axes[1, i].set_title(f'Network Response to {pattern_names[i]}')
        axes[1, i].set_xlabel('Time (ms)')
        axes[1, i].set_ylabel('Network Neuron')
        axes[1, i].set_ylim(-0.5, 24.5)
        axes[1, i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return network, patterns, responses


def example_5_memory_recall():
    """Example 5: Memory storage and recall demonstration"""
    print("\n" + "="*50)
    print("EXAMPLE 5: Memory Storage and Recall")
    print("="*50)
    
    # Create network with strong memory capabilities
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=60,
        input_size=8,
        output_size=4,
        enable_memory=True,
        device='cpu'
    )
    
    # Create distinct memory patterns
    memory_patterns = {
        'pattern_alpha': torch.rand(1, 60) * 0.8,
        'pattern_beta': torch.rand(1, 60) * 0.6,
        'pattern_gamma': torch.rand(1, 60) * 0.9
    }
    
    print("Storing memory patterns...")
    
    # Store patterns
    stored_ids = {}
    for name, pattern in memory_patterns.items():
        pattern_id = network.store_memory_pattern(pattern, name)
        stored_ids[name] = pattern_id
        print(f"Stored {name} with ID: {pattern_id}")
    
    # Let network settle
    print("Allowing network to settle...")
    for _ in range(50):
        background_input = torch.rand(1, 8) * 0.1
        network(background_input, num_steps=1)
    
    # Test recall
    print("\nTesting memory recall...")
    
    recall_results = {}
    for name, pattern_id in stored_ids.items():
        print(f"Recalling {name}...")
        
        # Recall pattern
        recalled_pattern = network.recall_memory_pattern(pattern_id, recall_strength=1.2)
        
        if recalled_pattern is not None:
            # Test network response to recalled pattern
            original_pattern = memory_patterns[name]
            
            # Compare similarity
            similarity = torch.cosine_similarity(
                original_pattern.flatten(),
                recalled_pattern.flatten(),
                dim=0
            ).item()
            
            recall_results[name] = {
                'similarity': similarity,
                'recalled': True
            }
            
            print(f"  Similarity to original: {similarity:.3f}")
        else:
            recall_results[name] = {
                'similarity': 0.0,
                'recalled': False
            }
            print(f"  Failed to recall")
    
    # Visualize memory network state
    memory_stats = network.get_network_statistics()['memory']
    
    print(f"\nMemory Network State:")
    print(f"  Active loops: {memory_stats['num_active_loops']}")
    print(f"  Stored patterns: {len(memory_stats['stored_patterns'])}")
    print(f"  Global activity: {memory_stats['global_activity_level']:.3f}")
    
    # Plot recall performance
    pattern_names = list(recall_results.keys())
    similarities = [recall_results[name]['similarity'] for name in pattern_names]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(pattern_names, similarities, color=['green' if s > 0.5 else 'red' for s in similarities])
    plt.xlabel('Memory Pattern')
    plt.ylabel('Recall Similarity')
    plt.title('Example 5: Memory Recall Performance')
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, similarity in zip(bars, similarities):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{similarity:.3f}', ha='center', va='bottom')
    
    plt.show()
    
    return network, memory_patterns, recall_results


def run_all_examples():
    """Run all examples in sequence"""
    print("RUNNING ALL RECURRENT SNN EXAMPLES")
    print("="*60)
    
    try:
        # Example 1: Basic usage
        network1, results1 = example_1_basic_usage()
        
        # Example 2: Continuous activity
        network2, results2 = example_2_continuous_activity()
        
        # Example 3: Learning dynamics
        network3, conn_hist, learn_hist = example_3_learning_dynamics()
        
        # Example 4: Temporal patterns
        network4, patterns, responses = example_4_temporal_patterns()
        
        # Example 5: Memory recall
        network5, mem_patterns, recall_results = example_5_memory_recall()
        
        print("\n" + "="*60)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        return {
            'basic': (network1, results1),
            'continuous': (network2, results2),
            'learning': (network3, conn_hist, learn_hist),
            'temporal': (network4, patterns, responses),
            'memory': (network5, mem_patterns, recall_results)
        }
        
    except Exception as e:
        print(f"Error running examples: {e}")
        raise


if __name__ == "__main__":
    # Run all examples
    example_results = run_all_examples()
    
    print("\nExample results available in 'example_results' dictionary")
    print("Keys:", list(example_results.keys()))
