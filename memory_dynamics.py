"""
Memory Dynamics Through Recurrent Loops
Implements memory formation and retrieval through reverberating neural activity
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Dict, List, Optional, Any
import networkx as nx


class MemoryLoop(nn.Module):
    """
    Memory formation through recurrent neural loops
    Implements short-term memory via reverberating activity
    """
    
    def __init__(
        self,
        loop_neurons: List[int],
        loop_strength: float = 1.2,
        decay_rate: float = 0.98,
        noise_level: float = 0.01,
        device: str = 'cpu'
    ):
        """
        Initialize memory loop
        
        Args:
            loop_neurons: List of neuron indices forming the loop
            loop_strength: Strength of recurrent connections in loop
            decay_rate: Activity decay rate in loop
            noise_level: Noise level for stochastic dynamics
            device: Device for computations
        """
        super(MemoryLoop, self).__init__()
        
        self.loop_neurons = loop_neurons
        self.loop_strength = loop_strength
        self.decay_rate = decay_rate
        self.noise_level = noise_level
        self.device = device
        
        # Loop state
        self.loop_activity = None
        self.activity_history = []
        self.memory_strength = 0.0
        
        # Statistics
        self.activation_count = 0
        self.total_activity = 0.0
        
    def reset_loop(self, batch_size: int):
        """Reset loop activity"""
        self.loop_activity = torch.zeros(
            batch_size, len(self.loop_neurons), device=self.device
        )
        self.activity_history = []
        self.memory_strength = 0.0
        
    def activate_loop(self, input_activity: torch.Tensor, strength: float = 1.0):
        """
        Activate memory loop with input pattern
        
        Args:
            input_activity: Input activity pattern [batch_size, num_loop_neurons]
            strength: Activation strength
        """
        batch_size = input_activity.shape[0]
        
        if self.loop_activity is None:
            self.reset_loop(batch_size)
            
        # Inject input activity into loop
        self.loop_activity += strength * input_activity
        self.memory_strength = torch.norm(self.loop_activity).item()
        self.activation_count += 1
        
    def update_loop(self) -> torch.Tensor:
        """
        Update loop activity through recurrent dynamics
        
        Returns:
            current_activity: Current loop activity [batch_size, num_loop_neurons]
        """
        if self.loop_activity is None:
            return torch.tensor([])
            
        # Apply recurrent connections (simplified circular loop)
        num_neurons = len(self.loop_neurons)
        if num_neurons > 1:
            # Circular shift for loop connectivity
            shifted_activity = torch.roll(self.loop_activity, shifts=1, dims=1)
            recurrent_input = self.loop_strength * shifted_activity
        else:
            # Self-recurrent for single neuron
            recurrent_input = self.loop_strength * self.loop_activity
            
        # Update activity with decay and noise
        noise = torch.randn_like(self.loop_activity) * self.noise_level
        self.loop_activity = (
            self.decay_rate * self.loop_activity +
            recurrent_input +
            noise
        )
        
        # Track activity
        self.activity_history.append(self.loop_activity.clone().detach())
        if len(self.activity_history) > 1000:  # Keep last 1000 steps
            self.activity_history.pop(0)
            
        self.total_activity += torch.sum(self.loop_activity).item()
        
        return self.loop_activity.clone()
    
    def get_memory_strength(self) -> float:
        """Get current memory strength"""
        if self.loop_activity is None:
            return 0.0
        return torch.norm(self.loop_activity).item()
    
    def is_active(self, threshold: float = 0.1) -> bool:
        """Check if loop is currently active"""
        return self.get_memory_strength() > threshold


class MemoryNetwork(nn.Module):
    """
    Network of memory loops for complex memory dynamics
    """
    
    def __init__(
        self,
        num_neurons: int,
        num_loops: int = 3,
        loop_size_range: Tuple[int, int] = (3, 8),
        inter_loop_connectivity: float = 0.1,
        device: str = 'cpu'
    ):
        """
        Initialize memory network
        
        Args:
            num_neurons: Total number of neurons
            num_loops: Number of memory loops
            loop_size_range: Range of loop sizes (min, max)
            inter_loop_connectivity: Connectivity between loops
            device: Device for computations
        """
        super(MemoryNetwork, self).__init__()
        
        self.num_neurons = num_neurons
        self.num_loops = num_loops
        self.device = device
        
        # Create memory loops
        self.loops = nn.ModuleList()
        self.loop_assignments = self._create_loops(loop_size_range)
        
        # Inter-loop connections
        self.inter_loop_weights = nn.Parameter(
            torch.randn(num_loops, num_loops, device=device) * 0.1
        )
        self.inter_loop_connectivity = inter_loop_connectivity
        
        # Memory patterns
        self.stored_patterns = {}
        self.pattern_counter = 0
        
        # Network statistics
        self.global_activity_history = []
        
    def _create_loops(self, loop_size_range: Tuple[int, int]) -> Dict[int, List[int]]:
        """Create non-overlapping memory loops"""
        min_size, max_size = loop_size_range
        loop_assignments = {}
        used_neurons = set()
        
        for loop_id in range(self.num_loops):
            # Determine loop size
            available_neurons = self.num_neurons - len(used_neurons)
            max_possible = min(max_size, available_neurons)
            
            if max_possible < min_size:
                break  # Not enough neurons for more loops
                
            loop_size = np.random.randint(min_size, max_possible + 1)
            
            # Select neurons for this loop
            available_indices = [i for i in range(self.num_neurons) if i not in used_neurons]
            loop_neurons = np.random.choice(available_indices, loop_size, replace=False).tolist()
            
            # Create loop
            loop = MemoryLoop(
                loop_neurons=loop_neurons,
                device=self.device
            )
            self.loops.append(loop)
            
            loop_assignments[loop_id] = loop_neurons
            used_neurons.update(loop_neurons)
            
        return loop_assignments
    
    def store_pattern(
        self,
        pattern: torch.Tensor,
        pattern_id: Optional[str] = None
    ) -> str:
        """
        Store a pattern in memory network
        
        Args:
            pattern: Activity pattern to store [batch_size, num_neurons]
            pattern_id: Optional pattern identifier
            
        Returns:
            pattern_id: Unique pattern identifier
        """
        if pattern_id is None:
            pattern_id = f"memory_{self.pattern_counter}"
            self.pattern_counter += 1
            
        # Distribute pattern across loops
        loop_patterns = {}
        for loop_id, loop_neurons in self.loop_assignments.items():
            if loop_id < len(self.loops):
                # Extract pattern for this loop's neurons
                loop_pattern = pattern[:, loop_neurons]
                loop_patterns[loop_id] = loop_pattern
                
                # Activate the loop with this pattern
                self.loops[loop_id].activate_loop(loop_pattern, strength=1.5)
                
        self.stored_patterns[pattern_id] = {
            'full_pattern': pattern.clone().detach(),
            'loop_patterns': loop_patterns,
            'storage_time': len(self.global_activity_history)
        }
        
        return pattern_id
    
    def recall_pattern(
        self,
        pattern_id: str,
        recall_strength: float = 1.0
    ) -> Optional[torch.Tensor]:
        """
        Recall a stored pattern
        
        Args:
            pattern_id: Pattern to recall
            recall_strength: Strength of recall activation
            
        Returns:
            recalled_pattern: Recalled pattern or None if not found
        """
        if pattern_id not in self.stored_patterns:
            return None
            
        stored_info = self.stored_patterns[pattern_id]
        loop_patterns = stored_info['loop_patterns']
        
        # Reactivate loops with stored patterns
        for loop_id, loop_pattern in loop_patterns.items():
            if loop_id < len(self.loops):
                self.loops[loop_id].activate_loop(loop_pattern, recall_strength)
                
        return stored_info['full_pattern']
    
    def update_memory_dynamics(self) -> Dict[str, torch.Tensor]:
        """
        Update all memory loops and inter-loop interactions
        
        Returns:
            loop_activities: Dictionary of current loop activities
        """
        loop_activities = {}
        
        # Update individual loops
        for loop_id, loop in enumerate(self.loops):
            activity = loop.update_loop()
            if len(activity) > 0:
                loop_activities[f"loop_{loop_id}"] = activity
                
        # Inter-loop interactions
        if len(loop_activities) > 1:
            self._update_inter_loop_interactions(loop_activities)
            
        # Track global activity
        global_activity = torch.cat([
            act.flatten() for act in loop_activities.values()
        ]) if loop_activities else torch.tensor([])
        
        self.global_activity_history.append(global_activity.clone().detach())
        if len(self.global_activity_history) > 1000:
            self.global_activity_history.pop(0)
            
        return loop_activities
    
    def _update_inter_loop_interactions(self, loop_activities: Dict[str, torch.Tensor]):
        """Update interactions between memory loops"""
        loop_ids = list(loop_activities.keys())
        
        for i, loop_id_i in enumerate(loop_ids):
            for j, loop_id_j in enumerate(loop_ids):
                if i != j and i < len(self.loops) and j < len(self.loops):
                    # Get interaction strength
                    interaction_strength = self.inter_loop_weights[i, j] * self.inter_loop_connectivity
                    
                    # Apply cross-loop influence
                    source_activity = loop_activities[loop_id_i].mean()
                    if source_activity > 0.1:  # Only if source is active
                        influence = interaction_strength * source_activity
                        self.loops[j].loop_activity += influence * 0.1  # Small influence
    
    def get_memory_state(self) -> Dict[str, Any]:
        """Get current memory network state"""
        loop_states = {}
        for loop_id, loop in enumerate(self.loops):
            loop_states[f"loop_{loop_id}"] = {
                'neurons': self.loop_assignments.get(loop_id, []),
                'activity': loop.get_memory_strength(),
                'is_active': loop.is_active(),
                'activation_count': loop.activation_count
            }
            
        return {
            'loop_states': loop_states,
            'stored_patterns': list(self.stored_patterns.keys()),
            'num_active_loops': sum(1 for loop in self.loops if loop.is_active()),
            'global_activity_level': torch.cat([
                act for act in self.global_activity_history[-10:]
            ]).mean().item() if self.global_activity_history else 0.0
        }
    
    def reset_memory(self):
        """Reset all memory loops"""
        for loop in self.loops:
            if hasattr(loop, 'reset_loop'):
                loop.reset_loop(1)  # Reset with batch size 1
        self.global_activity_history = []
