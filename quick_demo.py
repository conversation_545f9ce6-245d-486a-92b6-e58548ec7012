"""
Quick demonstration without visualization
"""

import torch
import numpy as np

from recurrent_snn import RecurrentSpikingNeuralNetwork

def quick_demo():
    print("="*60)
    print("QUICK RECURRENT SNN DEMO")
    print("="*60)
    
    # Create network
    print("\n1. Creating network...")
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=30,
        input_size=6,
        output_size=3,
        device='cpu'
    )
    print(f"✓ Network created with {network.num_neurons} neurons")
    
    # Test basic functionality
    print("\n2. Testing basic spike generation...")
    input_data = torch.rand(1, 6) * 0.8
    results = network(input_data, num_steps=50, return_traces=True)
    print(f"✓ Generated {results['total_spikes']} spikes")
    
    # Enable continuous activity
    print("\n3. Enabling continuous activity...")
    network.enable_continuous_activity(background_current=0.1, noise_level=0.05)
    
    # Store memory pattern
    print("\n4. Testing memory storage...")
    pattern = torch.rand(1, 30) * 0.7
    pattern_id = network.store_memory_pattern(pattern, "test_memory")
    print(f"✓ Stored memory pattern: {pattern_id}")
    
    # Run continuous simulation
    print("\n5. Running continuous simulation...")
    sim_results = network.run_continuous_simulation(duration=100)
    total_spikes = sum(spikes.sum().item() for spikes in sim_results['spike_history'])
    print(f"✓ Continuous simulation: {total_spikes} total spikes")
    
    # Test memory recall
    print("\n6. Testing memory recall...")
    recalled = network.recall_memory_pattern(pattern_id)
    if recalled is not None:
        similarity = torch.cosine_similarity(pattern.flatten(), recalled.flatten(), dim=0).item()
        print(f"✓ Memory recalled with similarity: {similarity:.3f}")
    else:
        print("✗ Memory recall failed")
    
    # Show statistics
    print("\n7. Final statistics...")
    stats = network.get_network_statistics()
    print(f"  Total spikes: {stats['total_spikes']}")
    print(f"  Learning events: {stats['learning_events']}")
    print(f"  Memory formations: {stats['memory_formations']}")
    print(f"  Connectivity: {stats['spa']['connectivity']:.3f}")
    print(f"  Growth events: {stats['spa']['growth_events']}")
    print(f"  Pruning events: {stats['spa']['pruning_events']}")
    
    # Test non-linear behavior
    print("\n8. Testing non-linear behavior...")
    patterns = [
        torch.zeros(1, 6),
        torch.ones(1, 6) * 0.5,
        torch.rand(1, 6) * 0.8,
        torch.ones(1, 6) * 0.3
    ]
    
    responses = []
    for i, pat in enumerate(patterns):
        result = network(pat, num_steps=20)
        response = result['output'].mean().item()
        responses.append(response)
        print(f"  Pattern {i}: response = {response:.4f}")
    
    variance = np.var(responses)
    print(f"  Response variance: {variance:.6f}")
    
    print("\n" + "="*60)
    print("KEY FEATURES DEMONSTRATED:")
    print("✓ Recurrent connections (non-feedforward)")
    print("✓ Continuous activity simulation")
    print("✓ Refractory periods")
    print("✓ Synaptogenesis Pruning Algorithm (SPA)")
    print("✓ Spike Timing Dependent Plasticity (STDP)")
    print("✓ Memory formation through loops")
    print("✓ Temporal spike dynamics")
    print("✓ Non-linear pattern processing")
    print("="*60)
    
    return network, stats

if __name__ == "__main__":
    network, stats = quick_demo()
    print(f"\nDemo completed! Final spike count: {stats['total_spikes']}")
