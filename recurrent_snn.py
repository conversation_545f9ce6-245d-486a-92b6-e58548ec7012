"""
Recurrent Spiking Neural Network (RSNN)
Main implementation combining all biological features
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Dict, List, Optional, Any
import time

from spiking_neuron import SpikingNeuron, AdaptiveSpikingNeuron
from synaptogenesis_pruning import SynaptogenesisPruningAlgorithm
from spike_timing import STDP<PERSON>earning, TemporalCoding
from memory_dynamics import MemoryNetwork


class RecurrentSpikingNeuralNetwork(nn.Module):
    """
    Recurrent Spiking Neural Network with biological dynamics

    Features:
    - Recurrent connections with continuous activity
    - Synaptogenesis Pruning Algorithm (SPA)
    - Spike Timing Dependent Plasticity (STDP)
    - Memory formation through reverberating loops
    - Refractory periods and temporal dynamics
    """

    def __init__(
        self,
        num_neurons: int = 100,
        input_size: int = 10,
        output_size: int = 5,
        neuron_type: str = 'adaptive',  # 'basic' or 'adaptive'
        initial_connectivity: float = 0.1,
        enable_spa: bool = True,
        enable_stdp: bool = True,
        enable_memory: bool = True,
        device: str = 'cpu',
        **kwargs
    ):
        """
        Initialize Recurrent Spiking Neural Network

        Args:
            num_neurons: Number of neurons in the network
            input_size: Size of input layer
            output_size: Size of output layer
            neuron_type: Type of neurons ('basic' or 'adaptive')
            initial_connectivity: Initial connection probability
            enable_spa: Enable Synaptogenesis Pruning Algorithm
            enable_stdp: Enable Spike Timing Dependent Plasticity
            enable_memory: Enable memory loops
            device: Device for computations
            **kwargs: Additional arguments for components
        """
        super(RecurrentSpikingNeuralNetwork, self).__init__()

        self.num_neurons = num_neurons
        self.input_size = input_size
        self.output_size = output_size
        self.device = device
        self.enable_spa = enable_spa
        self.enable_stdp = enable_stdp
        self.enable_memory = enable_memory

        # Initialize neurons
        if neuron_type == 'adaptive':
            self.neurons = AdaptiveSpikingNeuron(device=device, **kwargs)
        else:
            self.neurons = SpikingNeuron(device=device, **kwargs)

        # Input and output projections
        self.input_projection = nn.Linear(input_size, num_neurons, bias=False)
        self.output_projection = nn.Linear(num_neurons, output_size, bias=False)

        # Initialize recurrent connections
        self.recurrent_weights = nn.Parameter(
            torch.randn(num_neurons, num_neurons, device=device) * 0.1
        )

        # Synaptogenesis Pruning Algorithm
        if enable_spa:
            self.spa = SynaptogenesisPruningAlgorithm(
                num_neurons=num_neurons,
                initial_connectivity=initial_connectivity,
                device=device
            )
        else:
            # Static connectivity matrix
            self.connection_matrix = torch.rand(num_neurons, num_neurons, device=device) < initial_connectivity
            self.connection_matrix.fill_diagonal_(False)

        # Spike Timing Dependent Plasticity
        if enable_stdp:
            self.stdp = STDPLearning(device=device)

        # Temporal coding
        self.temporal_coding = TemporalCoding(device=device)

        # Memory network
        if enable_memory:
            self.memory_network = MemoryNetwork(
                num_neurons=num_neurons,
                device=device
            )

        # Network state
        self.current_spikes = None
        self.previous_spikes = None
        self.network_activity_history = []
        self.simulation_time = 0

        # Statistics
        self.total_spikes = 0
        self.learning_events = 0
        self.memory_formations = 0

    def get_effective_recurrent_weights(self) -> torch.Tensor:
        """Get effective recurrent weights considering connectivity"""
        if self.enable_spa:
            connection_matrix = self.spa.connection_matrix
            base_weights = self.spa.get_effective_weights()
        else:
            connection_matrix = self.connection_matrix
            base_weights = self.recurrent_weights * connection_matrix.float()

        return base_weights

    def forward(
        self,
        input_data: torch.Tensor,
        num_steps: int = 100,
        learning_rate: float = 0.01,
        return_traces: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the recurrent spiking network

        Args:
            input_data: Input data [batch_size, input_size] or [batch_size, num_steps, input_size]
            num_steps: Number of simulation time steps
            learning_rate: Learning rate for plasticity
            return_traces: Whether to return detailed traces

        Returns:
            results: Dictionary containing outputs and network state
        """
        batch_size = input_data.shape[0]

        # Handle input dimensions
        if len(input_data.shape) == 2:
            # Static input - repeat for all time steps
            input_sequence = input_data.unsqueeze(1).repeat(1, num_steps, 1)
        else:
            # Temporal input sequence
            input_sequence = input_data
            num_steps = input_sequence.shape[1]

        # Reset network state
        self.neurons.reset_state(batch_size, self.num_neurons)
        if self.enable_stdp:
            self.stdp.reset_traces(batch_size, self.num_neurons)
        if self.enable_memory:
            self.memory_network.reset_memory()

        # Storage for traces
        spike_traces = []
        membrane_traces = []
        output_traces = []
        recurrent_activity_traces = []

        # Simulation loop
        for t in range(num_steps):
            # Get input for this time step
            current_input = input_sequence[:, t, :]  # [batch_size, input_size]

            # Project input to neuron space
            input_current = self.input_projection(current_input)  # [batch_size, num_neurons]

            # Add recurrent input from previous time step
            if self.current_spikes is not None:
                recurrent_weights = self.get_effective_recurrent_weights()
                recurrent_input = torch.matmul(self.current_spikes, recurrent_weights.T)
                input_current += recurrent_input
                recurrent_activity_traces.append(recurrent_input.clone().detach())
            else:
                recurrent_activity_traces.append(torch.zeros_like(input_current))

            # Add memory network influence
            if self.enable_memory:
                memory_activities = self.memory_network.update_memory_dynamics()
                if memory_activities:
                    # Combine memory influences
                    memory_influence = torch.zeros_like(input_current)
                    for loop_activity in memory_activities.values():
                        if len(loop_activity.shape) == 2 and loop_activity.shape[1] <= self.num_neurons:
                            # Pad or truncate to match neuron count
                            if loop_activity.shape[1] < self.num_neurons:
                                padding = torch.zeros(
                                    batch_size,
                                    self.num_neurons - loop_activity.shape[1],
                                    device=self.device
                                )
                                loop_activity = torch.cat([loop_activity, padding], dim=1)
                            elif loop_activity.shape[1] > self.num_neurons:
                                loop_activity = loop_activity[:, :self.num_neurons]

                            memory_influence += loop_activity * 0.1  # Small influence
                    input_current += memory_influence

            # Update neurons
            spikes, membrane_potential = self.neurons(input_current)

            # Store previous spikes and update current
            self.previous_spikes = self.current_spikes
            self.current_spikes = spikes

            # Apply learning rules
            if self.previous_spikes is not None:
                # STDP learning
                if self.enable_stdp:
                    if self.enable_spa:
                        connection_matrix = self.spa.connection_matrix
                    else:
                        connection_matrix = self.connection_matrix

                    weight_update = self.stdp.compute_weight_update(
                        self.previous_spikes, spikes, connection_matrix
                    )

                    if self.enable_spa:
                        # Apply STDP to SPA weights
                        with torch.no_grad():
                            self.spa.synaptic_weights += learning_rate * weight_update
                            self.spa.synaptic_weights.clamp_(-1.0, 1.0)
                    else:
                        # Apply STDP to static recurrent weights
                        with torch.no_grad():
                            self.recurrent_weights += learning_rate * weight_update
                            self.recurrent_weights.clamp_(-1.0, 1.0)

                    self.learning_events += 1

                # SPA updates
                if self.enable_spa:
                    self.spa.update_connections(self.previous_spikes, spikes, learning_rate)

            # Store traces
            if return_traces:
                spike_traces.append(spikes.clone().detach())
                membrane_traces.append(membrane_potential.clone().detach())

            # Compute output
            output = self.output_projection(spikes)
            output_traces.append(output.clone().detach())

            # Update statistics
            self.total_spikes += spikes.sum().item()
            self.simulation_time += 1

        # Store network activity
        if return_traces and spike_traces:
            network_activity = torch.stack(spike_traces)  # [num_steps, batch_size, num_neurons]
            self.network_activity_history.append(network_activity)

        # Prepare results
        results = {
            'output': torch.stack(output_traces).transpose(0, 1),  # [batch_size, num_steps, output_size]
            'final_spikes': self.current_spikes,
            'total_spikes': self.total_spikes,
            'simulation_time': self.simulation_time
        }

        if return_traces:
            results.update({
                'spike_traces': torch.stack(spike_traces).transpose(0, 1) if spike_traces else None,
                'membrane_traces': torch.stack(membrane_traces).transpose(0, 1) if membrane_traces else None,
                'recurrent_traces': torch.stack(recurrent_activity_traces).transpose(0, 1)
            })

        return results

    def store_memory_pattern(
        self,
        pattern: torch.Tensor,
        pattern_id: Optional[str] = None
    ) -> str:
        """Store a pattern in memory network"""
        if not self.enable_memory:
            raise ValueError("Memory network is not enabled")

        pattern_id = self.memory_network.store_pattern(pattern, pattern_id)
        self.memory_formations += 1
        return pattern_id

    def recall_memory_pattern(
        self,
        pattern_id: str,
        recall_strength: float = 1.0
    ) -> Optional[torch.Tensor]:
        """Recall a stored memory pattern"""
        if not self.enable_memory:
            raise ValueError("Memory network is not enabled")

        return self.memory_network.recall_pattern(pattern_id, recall_strength)

    def get_network_statistics(self) -> Dict[str, Any]:
        """Get comprehensive network statistics"""
        stats = {
            'total_spikes': self.total_spikes,
            'simulation_time': self.simulation_time,
            'learning_events': self.learning_events,
            'memory_formations': self.memory_formations,
            'spike_rate': self.total_spikes / max(self.simulation_time, 1),
            'neuron_stats': {
                'num_neurons': self.num_neurons,
                'current_activity': self.current_spikes.sum().item() if self.current_spikes is not None else 0
            }
        }

        # Add SPA statistics
        if self.enable_spa:
            stats['spa'] = self.spa.get_statistics()

        # Add STDP statistics
        if self.enable_stdp:
            stats['stdp'] = self.stdp.get_learning_statistics()

        # Add memory statistics
        if self.enable_memory:
            stats['memory'] = self.memory_network.get_memory_state()

        return stats

    def reset_network(self):
        """Reset network to initial state"""
        self.current_spikes = None
        self.previous_spikes = None
        self.network_activity_history = []
        self.simulation_time = 0
        self.total_spikes = 0
        self.learning_events = 0
        self.memory_formations = 0

        # Reset components
        if self.enable_spa:
            self.spa.reset_statistics()
        if self.enable_memory:
            self.memory_network.reset_memory()

    def enable_continuous_activity(
        self,
        background_current: float = 0.1,
        noise_level: float = 0.05
    ):
        """
        Enable continuous background activity to maintain network dynamics

        Args:
            background_current: Constant background current
            noise_level: Level of random noise injection
        """
        self.background_current = background_current
        self.noise_level = noise_level
        self._continuous_mode = True

    def run_continuous_simulation(
        self,
        duration: int = 1000,
        input_patterns: Optional[List[torch.Tensor]] = None,
        pattern_intervals: Optional[List[int]] = None,
        learning_rate: float = 0.01
    ) -> Dict[str, Any]:
        """
        Run continuous simulation with ongoing activity

        Args:
            duration: Simulation duration in time steps
            input_patterns: Optional list of input patterns to inject
            pattern_intervals: Time intervals for pattern injection
            learning_rate: Learning rate for plasticity

        Returns:
            simulation_results: Comprehensive simulation results
        """
        if not hasattr(self, '_continuous_mode'):
            self.enable_continuous_activity()

        batch_size = 1
        results = {
            'spike_history': [],
            'connectivity_history': [],
            'memory_activity': [],
            'learning_events': []
        }

        # Initialize with background activity
        if self.current_spikes is None:
            self.neurons.reset_state(batch_size, self.num_neurons)

        for t in range(duration):
            # Create background input
            background_input = torch.ones(batch_size, self.input_size, device=self.device) * self.background_current

            # Add noise
            noise = torch.randn(batch_size, self.input_size, device=self.device) * self.noise_level
            current_input = background_input + noise

            # Inject patterns if specified
            if input_patterns and pattern_intervals:
                for pattern, interval in zip(input_patterns, pattern_intervals):
                    if t % interval == 0:
                        current_input += pattern

            # Run single time step
            step_results = self.forward(
                current_input,
                num_steps=1,
                learning_rate=learning_rate,
                return_traces=True
            )

            # Store results
            if step_results['spike_traces'] is not None:
                results['spike_history'].append(step_results['spike_traces'].squeeze())

            # Track connectivity changes
            if self.enable_spa and t % 50 == 0:
                results['connectivity_history'].append({
                    'time': t,
                    'connectivity': self.spa.get_connectivity(),
                    'num_connections': self.spa.connection_matrix.sum().item()
                })

            # Track memory activity
            if self.enable_memory:
                memory_state = self.memory_network.get_memory_state()
                results['memory_activity'].append({
                    'time': t,
                    'active_loops': memory_state['num_active_loops'],
                    'global_activity': memory_state['global_activity_level']
                })

        return results
