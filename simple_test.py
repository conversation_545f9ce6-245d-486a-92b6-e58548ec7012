"""
Simple test to verify basic functionality
"""

import torch
import numpy as np

print("Testing basic imports...")

try:
    from spiking_neuron import SpikingNeuron, AdaptiveSpikingNeuron
    print("✓ Spiking neuron import successful")
except Exception as e:
    print(f"✗ Spiking neuron import failed: {e}")

try:
    from synaptogenesis_pruning import SynaptogenesisPruningAlgorithm
    print("✓ SPA import successful")
except Exception as e:
    print(f"✗ SPA import failed: {e}")

try:
    from spike_timing import STDPLearning, TemporalCoding
    print("✓ STDP import successful")
except Exception as e:
    print(f"✗ STDP import failed: {e}")

try:
    from memory_dynamics import MemoryNetwork, MemoryLoop
    print("✓ Memory dynamics import successful")
except Exception as e:
    print(f"✗ Memory dynamics import failed: {e}")

try:
    from recurrent_snn import RecurrentSpikingNeuralNetwork
    print("✓ Main network import successful")
except Exception as e:
    print(f"✗ Main network import failed: {e}")

print("\nTesting basic neuron functionality...")

try:
    # Test basic neuron
    neuron = SpikingNeuron(threshold=1.0, device='cpu')
    input_current = torch.tensor([[1.5, 0.5, 2.0]])
    spikes, membrane = neuron(input_current)
    print(f"✓ Basic neuron test passed - spikes: {spikes}")
except Exception as e:
    print(f"✗ Basic neuron test failed: {e}")

print("\nTesting network creation...")

try:
    # Test network creation
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=20,
        input_size=5,
        output_size=3,
        device='cpu'
    )
    print("✓ Network creation successful")
    
    # Test forward pass
    input_data = torch.rand(1, 5)
    results = network(input_data, num_steps=10)
    print(f"✓ Forward pass successful - output shape: {results['output'].shape}")
    
except Exception as e:
    print(f"✗ Network test failed: {e}")

print("\nTesting continuous activity...")

try:
    network.enable_continuous_activity(background_current=0.1, noise_level=0.05)
    results = network.run_continuous_simulation(duration=50)
    print(f"✓ Continuous activity test passed - {len(results['spike_history'])} time steps")
except Exception as e:
    print(f"✗ Continuous activity test failed: {e}")

print("\nTesting memory functionality...")

try:
    pattern = torch.rand(1, 20)
    pattern_id = network.store_memory_pattern(pattern, "test_pattern")
    recalled = network.recall_memory_pattern(pattern_id)
    print(f"✓ Memory test passed - pattern stored and recalled")
except Exception as e:
    print(f"✗ Memory test failed: {e}")

print("\nTesting statistics...")

try:
    stats = network.get_network_statistics()
    print(f"✓ Statistics test passed - {len(stats)} categories")
    print(f"  Total spikes: {stats['total_spikes']}")
    print(f"  Learning events: {stats['learning_events']}")
    print(f"  Memory formations: {stats['memory_formations']}")
except Exception as e:
    print(f"✗ Statistics test failed: {e}")

print("\n" + "="*50)
print("BASIC FUNCTIONALITY TEST COMPLETED")
print("="*50)
