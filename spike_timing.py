"""
Spike Timing Dependent Plasticity (STDP) Implementation
Implements temporal learning rules based on spike timing
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Dict, List, Optional


class STDPLearning(nn.Module):
    """
    Spike Timing Dependent Plasticity (STDP) Learning Rule

    Implements asymmetric learning window where:
    - Pre-before-post spikes strengthen connections (LTP)
    - Post-before-pre spikes weaken connections (LTD)
    """

    def __init__(
        self,
        tau_plus: float = 20.0,
        tau_minus: float = 20.0,
        a_plus: float = 0.01,
        a_minus: float = 0.012,
        max_weight: float = 1.0,
        min_weight: float = -1.0,
        trace_decay: float = 0.95,
        device: str = 'cpu'
    ):
        """
        Initialize STDP learning rule

        Args:
            tau_plus: Time constant for LTP window (ms)
            tau_minus: Time constant for LTD window (ms)
            a_plus: Maximum LTP amplitude
            a_minus: Maximum LTD amplitude
            max_weight: Maximum synaptic weight
            min_weight: Minimum synaptic weight
            trace_decay: Decay factor for spike traces
            device: Device for computations
        """
        super(STDPLearning, self).__init__()

        self.tau_plus = tau_plus
        self.tau_minus = tau_minus
        self.a_plus = a_plus
        self.a_minus = a_minus
        self.max_weight = max_weight
        self.min_weight = min_weight
        self.trace_decay = trace_decay
        self.device = device

        # Spike traces for temporal correlation
        self.pre_trace = None
        self.post_trace = None

        # Learning statistics
        self.ltp_events = 0
        self.ltd_events = 0
        self.weight_changes = []

    def reset_traces(self, batch_size: int, num_neurons: int):
        """Reset spike traces"""
        self.pre_trace = torch.zeros(batch_size, num_neurons, device=self.device)
        self.post_trace = torch.zeros(batch_size, num_neurons, device=self.device)

    def update_traces(self, pre_spikes: torch.Tensor, post_spikes: torch.Tensor):
        """
        Update spike traces with exponential decay

        Args:
            pre_spikes: Presynaptic spikes [batch_size, num_neurons]
            post_spikes: Postsynaptic spikes [batch_size, num_neurons]
        """
        batch_size, num_neurons = pre_spikes.shape

        # Initialize traces if needed
        if self.pre_trace is None:
            self.reset_traces(batch_size, num_neurons)

        # Update traces with exponential decay
        self.pre_trace = self.trace_decay * self.pre_trace + pre_spikes.float()
        self.post_trace = self.trace_decay * self.post_trace + post_spikes.float()

    def compute_weight_update(
        self,
        pre_spikes: torch.Tensor,
        post_spikes: torch.Tensor,
        connection_matrix: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute STDP weight updates

        Args:
            pre_spikes: Presynaptic spikes [batch_size, num_neurons]
            post_spikes: Postsynaptic spikes [batch_size, num_neurons]
            connection_matrix: Binary connection matrix [num_neurons, num_neurons]

        Returns:
            weight_update: Weight change matrix [num_neurons, num_neurons]
        """
        batch_size, num_neurons = pre_spikes.shape

        # Update traces
        self.update_traces(pre_spikes, post_spikes)

        # Average over batch dimension
        pre_avg = pre_spikes.float().mean(dim=0)  # [num_neurons]
        post_avg = post_spikes.float().mean(dim=0)  # [num_neurons]
        pre_trace_avg = self.pre_trace.mean(dim=0)  # [num_neurons]
        post_trace_avg = self.post_trace.mean(dim=0)  # [num_neurons]

        # Compute LTP: post spike coincides with pre trace
        # Weight increase when pre-before-post
        ltp_update = torch.outer(pre_trace_avg, post_avg) * self.a_plus

        # Compute LTD: pre spike coincides with post trace
        # Weight decrease when post-before-pre
        ltd_update = torch.outer(pre_avg, post_trace_avg) * (-self.a_minus)

        # Total weight update
        weight_update = ltp_update + ltd_update

        # Apply only to existing connections
        weight_update = weight_update * connection_matrix.float()

        # Track statistics
        ltp_events = (ltp_update > 0).sum().item()
        ltd_events = (ltd_update < 0).sum().item()
        self.ltp_events += ltp_events
        self.ltd_events += ltd_events

        # Store weight change statistics
        if len(self.weight_changes) > 1000:  # Keep last 1000 updates
            self.weight_changes.pop(0)
        self.weight_changes.append({
            'ltp': ltp_events,
            'ltd': ltd_events,
            'total_change': weight_update.abs().sum().item()
        })

        return weight_update

    def apply_weight_update(
        self,
        weights: torch.Tensor,
        weight_update: torch.Tensor,
        learning_rate: float = 1.0
    ) -> torch.Tensor:
        """
        Apply weight updates with bounds checking

        Args:
            weights: Current weights [num_neurons, num_neurons]
            weight_update: Weight changes [num_neurons, num_neurons]
            learning_rate: Learning rate multiplier

        Returns:
            updated_weights: New weights [num_neurons, num_neurons]
        """
        # Apply update
        new_weights = weights + learning_rate * weight_update

        # Enforce bounds
        new_weights = torch.clamp(new_weights, self.min_weight, self.max_weight)

        return new_weights

    def get_learning_statistics(self) -> Dict:
        """Get STDP learning statistics"""
        return {
            'ltp_events': self.ltp_events,
            'ltd_events': self.ltd_events,
            'ltp_ltd_ratio': self.ltp_events / max(self.ltd_events, 1),
            'recent_changes': self.weight_changes[-10:] if self.weight_changes else [],
            'avg_change_magnitude': np.mean([
                c['total_change'] for c in self.weight_changes
            ]) if self.weight_changes else 0.0
        }


class TemporalCoding(nn.Module):
    """
    Temporal coding mechanisms for spike-based information processing
    """

    def __init__(
        self,
        time_window: int = 50,
        precision: float = 1.0,
        device: str = 'cpu'
    ):
        """
        Initialize temporal coding

        Args:
            time_window: Time window for temporal patterns (ms)
            precision: Temporal precision for spike timing
            device: Device for computations
        """
        super(TemporalCoding, self).__init__()

        self.time_window = time_window
        self.precision = precision
        self.device = device

        # Pattern storage
        self.temporal_patterns = {}
        self.pattern_counter = 0

    def encode_temporal_pattern(
        self,
        spike_times: torch.Tensor,
        pattern_id: Optional[str] = None
    ) -> str:
        """
        Encode spike timing pattern

        Args:
            spike_times: Spike timing matrix [time_steps, num_neurons]
            pattern_id: Optional pattern identifier

        Returns:
            pattern_id: Unique pattern identifier
        """
        if pattern_id is None:
            pattern_id = f"pattern_{self.pattern_counter}"
            self.pattern_counter += 1

        # Extract spike timing features
        spike_indices = torch.nonzero(spike_times, as_tuple=False)

        if len(spike_indices) > 0:
            # Create temporal signature
            temporal_signature = {
                'spike_times': spike_indices[:, 0].float(),  # Time indices
                'neuron_ids': spike_indices[:, 1],  # Neuron indices
                'pattern_length': spike_times.shape[0],
                'num_neurons': spike_times.shape[1]
            }

            self.temporal_patterns[pattern_id] = temporal_signature

        return pattern_id

    def decode_temporal_pattern(
        self,
        current_spikes: torch.Tensor,
        pattern_id: str,
        tolerance: float = 2.0
    ) -> float:
        """
        Decode and match temporal pattern

        Args:
            current_spikes: Current spike pattern [time_steps, num_neurons]
            pattern_id: Pattern to match against
            tolerance: Temporal tolerance for matching (ms)

        Returns:
            match_score: Pattern matching score [0, 1]
        """
        if pattern_id not in self.temporal_patterns:
            return 0.0

        stored_pattern = self.temporal_patterns[pattern_id]
        current_indices = torch.nonzero(current_spikes, as_tuple=False)

        if len(current_indices) == 0:
            return 0.0

        # Extract current timing features
        current_times = current_indices[:, 0].float()
        current_neurons = current_indices[:, 1]

        stored_times = stored_pattern['spike_times']
        stored_neurons = stored_pattern['neuron_ids']

        # Calculate temporal similarity
        matches = 0
        total_stored = len(stored_times)

        for i, (s_time, s_neuron) in enumerate(zip(stored_times, stored_neurons)):
            # Find matching spikes in current pattern
            neuron_matches = current_neurons == s_neuron
            if neuron_matches.any():
                matching_times = current_times[neuron_matches]
                time_diffs = torch.abs(matching_times - s_time)

                if torch.any(time_diffs <= tolerance):
                    matches += 1

        match_score = matches / max(total_stored, 1)
        return match_score

    def get_pattern_library(self) -> Dict:
        """Get stored temporal patterns"""
        return {
            'num_patterns': len(self.temporal_patterns),
            'pattern_ids': list(self.temporal_patterns.keys()),
            'patterns': self.temporal_patterns
        }
