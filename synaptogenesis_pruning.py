"""
Synaptogenesis Pruning Algorithm (SPA)
Implements dynamic synaptic connection management with biological realism
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Dict, List, Optional
import random


class SynaptogenesisPruningAlgorithm(nn.Module):
    """
    Synaptogenesis Pruning Algorithm (SPA)

    This algorithm implements:
    - Dynamic creation of new synaptic connections
    - Pruning of weak or unused connections
    - Activity-dependent synaptic strength modification
    - Homeostatic regulation of connection density
    """

    def __init__(
        self,
        num_neurons: int,
        initial_connectivity: float = 0.1,
        max_connectivity: float = 0.3,
        min_connectivity: float = 0.05,
        pruning_threshold: float = 0.01,
        growth_rate: float = 0.001,
        pruning_rate: float = 0.002,
        activity_window: int = 1000,
        homeostatic_target: float = 0.15,
        device: str = 'cpu'
    ):
        """
        Initialize SPA algorithm

        Args:
            num_neurons: Number of neurons in the network
            initial_connectivity: Initial connection probability
            max_connectivity: Maximum allowed connectivity
            min_connectivity: Minimum allowed connectivity
            pruning_threshold: Threshold below which connections are pruned
            growth_rate: Rate of new connection formation
            pruning_rate: Rate of connection pruning
            activity_window: Window size for activity monitoring
            homeostatic_target: Target connectivity for homeostasis
            device: Device for computations
        """
        super(SynaptogenesisPruningAlgorithm, self).__init__()

        self.num_neurons = num_neurons
        self.initial_connectivity = initial_connectivity
        self.max_connectivity = max_connectivity
        self.min_connectivity = min_connectivity
        self.pruning_threshold = pruning_threshold
        self.growth_rate = growth_rate
        self.pruning_rate = pruning_rate
        self.activity_window = activity_window
        self.homeostatic_target = homeostatic_target
        self.device = device

        # Initialize connection matrix and weights
        self.connection_matrix = self._initialize_connections()
        self.synaptic_weights = nn.Parameter(
            torch.randn(num_neurons, num_neurons, device=device) * 0.1
        )

        # Activity tracking
        self.activity_history = []
        self.connection_strength_history = []
        self.update_counter = 0

        # Statistics tracking
        self.pruning_events = 0
        self.growth_events = 0
        self.connection_history = []

    def _initialize_connections(self) -> torch.Tensor:
        """Initialize random connectivity matrix"""
        # Create random connections with initial connectivity probability
        connections = torch.rand(
            self.num_neurons, self.num_neurons, device=self.device
        ) < self.initial_connectivity

        # Remove self-connections
        connections.fill_diagonal_(False)

        return connections

    def get_effective_weights(self) -> torch.Tensor:
        """Get effective synaptic weights (only for existing connections)"""
        return self.synaptic_weights * self.connection_matrix.float()

    def update_connections(
        self,
        pre_spikes: torch.Tensor,
        post_spikes: torch.Tensor,
        learning_rate: float = 0.01
    ):
        """
        Update synaptic connections based on activity

        Args:
            pre_spikes: Presynaptic spike activity [batch_size, num_neurons]
            post_spikes: Postsynaptic spike activity [batch_size, num_neurons]
            learning_rate: Learning rate for weight updates
        """
        self.update_counter += 1

        # Store activity for analysis
        self.activity_history.append({
            'pre_spikes': pre_spikes.clone().detach(),
            'post_spikes': post_spikes.clone().detach()
        })

        # Keep only recent activity
        if len(self.activity_history) > self.activity_window:
            self.activity_history.pop(0)

        # Update synaptic strengths based on correlated activity
        self._update_synaptic_strengths(pre_spikes, post_spikes, learning_rate)

        # Perform synaptogenesis and pruning periodically
        if self.update_counter % 100 == 0:  # Every 100 updates
            self._perform_synaptogenesis()
            self._perform_pruning()
            self._homeostatic_regulation()

        # Track connection statistics
        if self.update_counter % 50 == 0:
            self.connection_history.append({
                'step': self.update_counter,
                'connectivity': self.get_connectivity(),
                'avg_weight': self.get_effective_weights().abs().mean().item(),
                'num_connections': self.connection_matrix.sum().item()
            })

    def _update_synaptic_strengths(
        self,
        pre_spikes: torch.Tensor,
        post_spikes: torch.Tensor,
        learning_rate: float
    ):
        """Update synaptic weights based on spike timing"""
        batch_size = pre_spikes.shape[0]

        # Average over batch dimension
        pre_activity = pre_spikes.float().mean(dim=0)  # [num_neurons]
        post_activity = post_spikes.float().mean(dim=0)  # [num_neurons]

        # Compute correlation matrix (outer product)
        correlation = torch.outer(pre_activity, post_activity)

        # Update weights only for existing connections
        weight_update = learning_rate * correlation * self.connection_matrix.float()

        # Apply weight update
        with torch.no_grad():
            self.synaptic_weights += weight_update

            # Clip weights to reasonable range
            self.synaptic_weights.clamp_(-1.0, 1.0)

    def _perform_synaptogenesis(self):
        """Create new synaptic connections"""
        current_connectivity = self.get_connectivity()

        if current_connectivity < self.max_connectivity:
            # Find potential new connections
            potential_connections = ~self.connection_matrix
            potential_connections.fill_diagonal_(False)  # No self-connections

            # Calculate growth probability based on activity
            growth_prob = self._calculate_growth_probability()

            # Select connections to add
            num_potential = potential_connections.sum().item()
            if num_potential > 0:
                num_to_add = int(self.growth_rate * num_potential)

                if num_to_add > 0:
                    # Get indices of potential connections
                    potential_indices = torch.nonzero(potential_connections, as_tuple=False)

                    # Randomly select connections to add
                    if len(potential_indices) >= num_to_add:
                        selected_indices = potential_indices[
                            torch.randperm(len(potential_indices))[:num_to_add]
                        ]

                        # Add new connections
                        for idx in selected_indices:
                            i, j = idx[0].item(), idx[1].item()
                            self.connection_matrix[i, j] = True
                            # Initialize new weight
                            self.synaptic_weights.data[i, j] = torch.randn(1, device=self.device) * 0.1

                        self.growth_events += num_to_add

    def _perform_pruning(self):
        """Remove weak synaptic connections"""
        # Get absolute weights for existing connections
        existing_weights = self.get_effective_weights().abs()

        # Find connections to prune
        weak_connections = (existing_weights < self.pruning_threshold) & self.connection_matrix

        # Prune connections
        num_pruned = weak_connections.sum().item()
        if num_pruned > 0:
            self.connection_matrix[weak_connections] = False
            self.pruning_events += num_pruned

    def _calculate_growth_probability(self) -> torch.Tensor:
        """Calculate probability of forming new connections based on activity"""
        if len(self.activity_history) < 10:
            return torch.ones(self.num_neurons, self.num_neurons, device=self.device) * 0.1

        # Calculate recent activity levels
        recent_activity = torch.stack([
            h['pre_spikes'].mean(dim=0) + h['post_spikes'].mean(dim=0)
            for h in self.activity_history[-10:]
        ]).mean(dim=0)

        # Growth probability based on activity correlation
        growth_prob = torch.outer(recent_activity, recent_activity)
        return torch.sigmoid(growth_prob - 0.5)  # Normalize to [0, 1]

    def _homeostatic_regulation(self):
        """Maintain target connectivity through homeostatic mechanisms"""
        current_connectivity = self.get_connectivity()

        if current_connectivity > self.homeostatic_target * 1.2:
            # Too many connections - increase pruning
            self.pruning_rate *= 1.1
            self.growth_rate *= 0.9
        elif current_connectivity < self.homeostatic_target * 0.8:
            # Too few connections - increase growth
            self.growth_rate *= 1.1
            self.pruning_rate *= 0.9

    def get_connectivity(self) -> float:
        """Get current connectivity ratio"""
        total_possible = self.num_neurons * (self.num_neurons - 1)  # Exclude self-connections
        current_connections = self.connection_matrix.sum().item()
        return current_connections / total_possible

    def get_statistics(self) -> Dict:
        """Get algorithm statistics"""
        return {
            'connectivity': self.get_connectivity(),
            'num_connections': self.connection_matrix.sum().item(),
            'pruning_events': self.pruning_events,
            'growth_events': self.growth_events,
            'avg_weight': self.get_effective_weights().abs().mean().item(),
            'weight_std': self.get_effective_weights().std().item(),
            'connection_history': self.connection_history
        }

    def reset_statistics(self):
        """Reset tracking statistics"""
        self.pruning_events = 0
        self.growth_events = 0
        self.connection_history = []
        self.update_counter = 0
