"""
Comprehensive Test Suite for Recurrent Spiking Neural Network
Tests all biological features and demonstrates non-linear behavior
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from recurrent_snn import RecurrentSpikingNeuralNetwork
from spiking_neuron import <PERSON>piking<PERSON>euron, AdaptiveSpikingNeuron
from synaptogenesis_pruning import SynaptogenesisPruningAlgorithm
from spike_timing import STDP<PERSON>earning, TemporalCoding
from memory_dynamics import MemoryNetwork, MemoryLoop
from visualization import SNNVisualizer


class TestSpikingNeuron:
    """Test spiking neuron implementations"""

    def test_basic_neuron_spiking(self):
        """Test basic neuron spike generation"""
        neuron = SpikingNeuron(threshold=1.0, device='cpu')

        # Test with input above threshold
        input_current = torch.tensor([[1.5, 0.5, 2.0]])  # [batch_size=1, num_neurons=3]
        spikes, membrane = neuron(input_current)

        # Check spike generation
        assert spikes[0, 0] == 1.0  # Should spike
        assert spikes[0, 1] == 0.0  # Should not spike
        assert spikes[0, 2] == 1.0  # Should spike

        print("✓ Basic neuron spiking test passed")

    def test_refractory_period(self):
        """Test neuron refractory period"""
        neuron = SpikingNeuron(threshold=1.0, refractory_period=3, device='cpu')

        # First spike
        input_current = torch.tensor([[2.0]])
        spikes1, _ = neuron(input_current)
        assert spikes1[0, 0] == 1.0

        # During refractory period - should not spike
        spikes2, _ = neuron(input_current)
        assert spikes2[0, 0] == 0.0

        spikes3, _ = neuron(input_current)
        assert spikes3[0, 0] == 0.0

        # After refractory period - should spike again
        spikes4, _ = neuron(input_current)
        # The neuron should be able to spike again after refractory period
        assert spikes4[0, 0] == 1.0

        print("✓ Refractory period test passed")

    def test_adaptive_neuron(self):
        """Test adaptive neuron with spike-frequency adaptation"""
        neuron = AdaptiveSpikingNeuron(
            threshold=1.0,
            adaptation_strength=0.2,
            device='cpu'
        )

        # Constant input should show adaptation
        input_current = torch.tensor([[1.5]])
        spike_counts = []

        for _ in range(10):
            spikes, _ = neuron(input_current)
            spike_counts.append(spikes.sum().item())

        # Spike rate should decrease due to adaptation
        early_rate = np.mean(spike_counts[:3])
        late_rate = np.mean(spike_counts[-3:])
        assert late_rate < early_rate

        print("✓ Adaptive neuron test passed")


class TestSynaptogenesisPruning:
    """Test SPA algorithm"""

    def test_spa_initialization(self):
        """Test SPA initialization"""
        spa = SynaptogenesisPruningAlgorithm(
            num_neurons=10,
            initial_connectivity=0.2,
            device='cpu'
        )

        # Check initial connectivity
        connectivity = spa.get_connectivity()
        assert 0.1 < connectivity < 0.3  # Should be around 0.2

        # Check no self-connections
        assert not spa.connection_matrix.diagonal().any()

        print("✓ SPA initialization test passed")

    def test_spa_dynamics(self):
        """Test SPA connection dynamics"""
        spa = SynaptogenesisPruningAlgorithm(
            num_neurons=20,
            initial_connectivity=0.1,
            growth_rate=0.01,
            pruning_rate=0.01,
            device='cpu'
        )

        initial_connections = spa.connection_matrix.sum().item()

        # Simulate activity
        for _ in range(200):
            pre_spikes = torch.rand(1, 20) > 0.9
            post_spikes = torch.rand(1, 20) > 0.9
            spa.update_connections(pre_spikes, post_spikes)

        final_connections = spa.connection_matrix.sum().item()
        stats = spa.get_statistics()

        # Should have some growth and pruning events
        assert stats['growth_events'] > 0
        assert stats['pruning_events'] > 0

        print(f"✓ SPA dynamics test passed - Growth: {stats['growth_events']}, Pruning: {stats['pruning_events']}")


class TestSTDPLearning:
    """Test STDP learning mechanisms"""

    def test_stdp_learning(self):
        """Test STDP weight updates"""
        stdp = STDPLearning(device='cpu')

        # Create connection matrix
        connection_matrix = torch.ones(5, 5, dtype=torch.bool)
        connection_matrix.fill_diagonal_(False)

        # Test LTP (pre before post)
        pre_spikes = torch.tensor([[1.0, 0.0, 0.0, 0.0, 0.0]])
        post_spikes = torch.tensor([[0.0, 0.0, 0.0, 0.0, 0.0]])

        # Update traces
        stdp.update_traces(pre_spikes, post_spikes)

        # Now post spike
        pre_spikes = torch.tensor([[0.0, 0.0, 0.0, 0.0, 0.0]])
        post_spikes = torch.tensor([[0.0, 1.0, 0.0, 0.0, 0.0]])

        weight_update = stdp.compute_weight_update(pre_spikes, post_spikes, connection_matrix)

        # Should have positive update for connection 0->1
        assert weight_update[0, 1] > 0

        stats = stdp.get_learning_statistics()
        assert stats['ltp_events'] > 0

        print("✓ STDP learning test passed")


class TestMemoryDynamics:
    """Test memory formation and retrieval"""

    def test_memory_loop(self):
        """Test individual memory loop"""
        loop = MemoryLoop(
            loop_neurons=[0, 1, 2],
            loop_strength=1.1,
            device='cpu'
        )

        # Activate loop
        pattern = torch.tensor([[1.0, 0.5, 0.8]])
        loop.activate_loop(pattern)

        # Update loop dynamics
        activities = []
        for _ in range(50):
            activity = loop.update_loop()
            activities.append(loop.get_memory_strength())

        # Activity should persist for some time
        assert max(activities[10:20]) > 0.1

        print("✓ Memory loop test passed")

    def test_memory_network(self):
        """Test memory network with multiple loops"""
        memory_net = MemoryNetwork(
            num_neurons=20,
            num_loops=3,
            device='cpu'
        )

        # Store pattern
        pattern = torch.rand(1, 20)
        pattern_id = memory_net.store_pattern(pattern)

        # Check pattern storage
        assert pattern_id in memory_net.stored_patterns

        # Recall pattern
        recalled = memory_net.recall_pattern(pattern_id)
        assert recalled is not None

        # Update dynamics
        for _ in range(10):
            memory_net.update_memory_dynamics()

        state = memory_net.get_memory_state()
        assert state['num_active_loops'] >= 0

        print("✓ Memory network test passed")


class TestRecurrentSNN:
    """Test complete recurrent spiking neural network"""

    def test_network_initialization(self):
        """Test network initialization"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=50,
            input_size=10,
            output_size=5,
            device='cpu'
        )

        # Check components
        assert network.num_neurons == 50
        assert network.input_size == 10
        assert network.output_size == 5
        assert network.enable_spa
        assert network.enable_stdp
        assert network.enable_memory

        print("✓ Network initialization test passed")

    def test_forward_pass(self):
        """Test network forward pass"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=30,
            input_size=5,
            output_size=3,
            device='cpu'
        )

        # Test with static input
        input_data = torch.rand(2, 5)  # [batch_size=2, input_size=5]
        results = network(input_data, num_steps=20, return_traces=True)

        # Check output shape
        assert results['output'].shape == (2, 20, 3)  # [batch, time, output]
        assert results['spike_traces'] is not None
        assert results['membrane_traces'] is not None

        print("✓ Forward pass test passed")

    def test_continuous_activity(self):
        """Test continuous activity simulation"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=25,
            input_size=5,
            output_size=3,
            device='cpu'
        )

        # Enable continuous activity
        network.enable_continuous_activity(background_current=0.2, noise_level=0.1)

        # Run continuous simulation
        results = network.run_continuous_simulation(duration=100)

        # Check for ongoing activity
        assert len(results['spike_history']) == 100
        assert len(results['connectivity_history']) > 0

        # Check that activity persists
        total_spikes = sum(spikes.sum().item() for spikes in results['spike_history'])
        assert total_spikes > 0

        print(f"✓ Continuous activity test passed - Total spikes: {total_spikes}")

    def test_memory_formation(self):
        """Test memory formation and retrieval"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=40,
            input_size=8,
            output_size=4,
            device='cpu'
        )

        # Create and store memory pattern
        pattern = torch.rand(1, 40)
        pattern_id = network.store_memory_pattern(pattern, "test_pattern")

        # Recall pattern
        recalled = network.recall_memory_pattern(pattern_id)
        assert recalled is not None

        # Check statistics
        stats = network.get_network_statistics()
        assert stats['memory_formations'] == 1
        assert 'memory' in stats

        print("✓ Memory formation test passed")

    def test_learning_dynamics(self):
        """Test learning and plasticity"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=30,
            input_size=6,
            output_size=3,
            device='cpu'
        )

        # Run simulation with learning
        input_data = torch.rand(1, 6)

        # Initial connectivity
        initial_stats = network.get_network_statistics()
        initial_connectivity = initial_stats['spa']['connectivity']

        # Run multiple episodes
        for _ in range(5):
            results = network(input_data, num_steps=50, learning_rate=0.02)

        # Final connectivity
        final_stats = network.get_network_statistics()

        # Should have learning events
        assert final_stats['learning_events'] > 0
        assert final_stats['spa']['growth_events'] + final_stats['spa']['pruning_events'] > 0

        print("✓ Learning dynamics test passed")


class TestNonLinearBehavior:
    """Test non-linear behavior and memory capabilities"""

    def test_xor_like_problem(self):
        """Test network's ability to solve non-linear problems"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=20,
            input_size=2,
            output_size=1,
            device='cpu'
        )

        # XOR-like patterns
        patterns = [
            torch.tensor([[0.0, 0.0]]),  # -> low output
            torch.tensor([[1.0, 0.0]]),  # -> high output
            torch.tensor([[0.0, 1.0]]),  # -> high output
            torch.tensor([[1.0, 1.0]]),  # -> low output
        ]

        outputs = []
        for pattern in patterns:
            result = network(pattern, num_steps=30)
            output = result['output'].mean().item()
            outputs.append(output)

        # Check for different responses to different patterns
        output_variance = np.var(outputs)
        assert output_variance > 0.001  # Should show variation

        print(f"✓ Non-linear behavior test passed - Output variance: {output_variance:.4f}")

    def test_temporal_pattern_recognition(self):
        """Test temporal pattern recognition capabilities"""
        network = RecurrentSpikingNeuralNetwork(
            num_neurons=30,
            input_size=5,
            output_size=2,
            device='cpu'
        )

        # Create temporal patterns
        pattern1 = torch.zeros(10, 5)
        pattern1[1, 0] = 1.0  # Spike at time 1, neuron 0
        pattern1[3, 1] = 1.0  # Spike at time 3, neuron 1
        pattern1[5, 2] = 1.0  # Spike at time 5, neuron 2

        pattern2 = torch.zeros(10, 5)
        pattern2[2, 0] = 1.0  # Different timing
        pattern2[4, 1] = 1.0
        pattern2[6, 2] = 1.0

        # Test responses
        result1 = network(pattern1.unsqueeze(0), num_steps=10)
        result2 = network(pattern2.unsqueeze(0), num_steps=10)

        # Should produce different outputs for different temporal patterns
        output1 = result1['output'].mean().item()
        output2 = result2['output'].mean().item()

        assert abs(output1 - output2) > 0.01  # Should be distinguishable

        print(f"✓ Temporal pattern recognition test passed")


def run_comprehensive_demo():
    """Run comprehensive demonstration of all features"""
    print("\n" + "="*60)
    print("COMPREHENSIVE RECURRENT SNN DEMONSTRATION")
    print("="*60)

    # Create network
    network = RecurrentSpikingNeuralNetwork(
        num_neurons=50,
        input_size=8,
        output_size=4,
        initial_connectivity=0.15,
        device='cpu'
    )

    print(f"\nNetwork created with {network.num_neurons} neurons")

    # Enable continuous activity
    network.enable_continuous_activity(background_current=0.15, noise_level=0.08)
    print("Continuous activity enabled")

    # Create input patterns for memory storage
    patterns = [
        torch.rand(1, 50) * 0.8,
        torch.rand(1, 50) * 0.6,
        torch.rand(1, 50) * 0.9
    ]

    # Store patterns in memory
    pattern_ids = []
    for i, pattern in enumerate(patterns):
        pid = network.store_memory_pattern(pattern, f"pattern_{i}")
        pattern_ids.append(pid)
        print(f"Stored memory pattern: {pid}")

    # Run continuous simulation
    print("\nRunning continuous simulation...")
    input_patterns = [torch.rand(1, 8) * 0.5 for _ in range(3)]

    results = network.run_continuous_simulation(
        duration=500,
        input_patterns=input_patterns,
        pattern_intervals=[100, 150, 200],
        learning_rate=0.01
    )

    # Get final statistics
    stats = network.get_network_statistics()

    print("\n" + "-"*40)
    print("FINAL NETWORK STATISTICS")
    print("-"*40)
    print(f"Total spikes: {stats['total_spikes']}")
    print(f"Spike rate: {stats['spike_rate']:.3f} spikes/ms")
    print(f"Learning events: {stats['learning_events']}")
    print(f"Memory formations: {stats['memory_formations']}")

    if 'spa' in stats:
        spa_stats = stats['spa']
        print(f"\nSPA Statistics:")
        print(f"  Connectivity: {spa_stats['connectivity']:.3f}")
        print(f"  Growth events: {spa_stats['growth_events']}")
        print(f"  Pruning events: {spa_stats['pruning_events']}")

    if 'stdp' in stats:
        stdp_stats = stats['stdp']
        print(f"\nSTDP Statistics:")
        print(f"  LTP events: {stdp_stats['ltp_events']}")
        print(f"  LTD events: {stdp_stats['ltd_events']}")
        print(f"  LTP/LTD ratio: {stdp_stats['ltp_ltd_ratio']:.3f}")

    if 'memory' in stats:
        memory_stats = stats['memory']
        print(f"\nMemory Statistics:")
        print(f"  Active loops: {memory_stats['num_active_loops']}")
        print(f"  Stored patterns: {len(memory_stats['stored_patterns'])}")

    print("\n" + "="*60)
    print("DEMONSTRATION COMPLETED SUCCESSFULLY")
    print("="*60)

    return network, results, stats


if __name__ == "__main__":
    # Run all tests
    print("Running Recurrent SNN Test Suite...")

    # Test individual components
    test_neuron = TestSpikingNeuron()
    test_neuron.test_basic_neuron_spiking()
    test_neuron.test_refractory_period()
    test_neuron.test_adaptive_neuron()

    test_spa = TestSynaptogenesisPruning()
    test_spa.test_spa_initialization()
    test_spa.test_spa_dynamics()

    test_stdp = TestSTDPLearning()
    test_stdp.test_stdp_learning()

    test_memory = TestMemoryDynamics()
    test_memory.test_memory_loop()
    test_memory.test_memory_network()

    test_network = TestRecurrentSNN()
    test_network.test_network_initialization()
    test_network.test_forward_pass()
    test_network.test_continuous_activity()
    test_network.test_memory_formation()
    test_network.test_learning_dynamics()

    test_nonlinear = TestNonLinearBehavior()
    test_nonlinear.test_xor_like_problem()
    test_nonlinear.test_temporal_pattern_recognition()

    print("\n✓ All tests passed!")

    # Run comprehensive demonstration
    network, results, stats = run_comprehensive_demo()
