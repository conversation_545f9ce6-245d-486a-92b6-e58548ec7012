"""
Visualization Tools for Recurrent Spiking Neural Networks
Provides comprehensive visualization of network dynamics and learning
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from typing import Dict, List, Optional, Tuple, Any
import matplotlib.animation as animation
from matplotlib.patches import Circle
import warnings
warnings.filterwarnings('ignore')


class SNNVisualizer:
    """
    Comprehensive visualization toolkit for spiking neural networks
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize visualizer
        
        Args:
            figsize: Default figure size for plots
        """
        self.figsize = figsize
        plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')
        
    def plot_spike_raster(
        self,
        spike_data: torch.Tensor,
        title: str = "Spike Raster Plot",
        neuron_subset: Optional[List[int]] = None,
        time_range: Optional[Tuple[int, int]] = None,
        save_path: Optional[str] = None
    ):
        """
        Create spike raster plot
        
        Args:
            spike_data: Spike data [time_steps, batch_size, num_neurons] or [time_steps, num_neurons]
            title: Plot title
            neuron_subset: Subset of neurons to plot
            time_range: Time range to plot (start, end)
            save_path: Path to save figure
        """
        # Handle different input dimensions
        if len(spike_data.shape) == 3:
            spikes = spike_data[:, 0, :]  # Take first batch
        else:
            spikes = spike_data
            
        spikes = spikes.cpu().numpy()
        time_steps, num_neurons = spikes.shape
        
        # Apply neuron subset
        if neuron_subset is not None:
            spikes = spikes[:, neuron_subset]
            neuron_labels = neuron_subset
        else:
            neuron_labels = list(range(num_neurons))
            
        # Apply time range
        if time_range is not None:
            start, end = time_range
            spikes = spikes[start:end, :]
            time_offset = start
        else:
            time_offset = 0
            
        # Create raster plot
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Find spike times and neuron indices
        spike_times, neuron_indices = np.where(spikes > 0)
        spike_times += time_offset
        
        # Plot spikes
        ax.scatter(spike_times, neuron_indices, s=1, c='black', alpha=0.7)
        
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Neuron Index')
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_membrane_potential(
        self,
        membrane_data: torch.Tensor,
        neuron_indices: List[int] = [0, 1, 2],
        title: str = "Membrane Potential Traces",
        save_path: Optional[str] = None
    ):
        """
        Plot membrane potential traces for selected neurons
        
        Args:
            membrane_data: Membrane potential data [time_steps, batch_size, num_neurons]
            neuron_indices: Indices of neurons to plot
            title: Plot title
            save_path: Path to save figure
        """
        if len(membrane_data.shape) == 3:
            membrane = membrane_data[:, 0, :].cpu().numpy()  # Take first batch
        else:
            membrane = membrane_data.cpu().numpy()
            
        fig, ax = plt.subplots(figsize=self.figsize)
        
        time_steps = np.arange(membrane.shape[0])
        
        for i, neuron_idx in enumerate(neuron_indices):
            if neuron_idx < membrane.shape[1]:
                ax.plot(time_steps, membrane[:, neuron_idx], 
                       label=f'Neuron {neuron_idx}', alpha=0.8)
                
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Membrane Potential (mV)')
        ax.set_title(title)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_connectivity_matrix(
        self,
        connection_matrix: torch.Tensor,
        weights: Optional[torch.Tensor] = None,
        title: str = "Network Connectivity",
        save_path: Optional[str] = None
    ):
        """
        Visualize network connectivity matrix
        
        Args:
            connection_matrix: Binary connectivity matrix [num_neurons, num_neurons]
            weights: Optional weight matrix for connection strengths
            title: Plot title
            save_path: Path to save figure
        """
        connections = connection_matrix.cpu().numpy()
        
        fig, axes = plt.subplots(1, 2 if weights is not None else 1, figsize=(15, 6))
        if weights is None:
            axes = [axes]
            
        # Plot connectivity
        im1 = axes[0].imshow(connections, cmap='Blues', aspect='equal')
        axes[0].set_title('Connection Matrix')
        axes[0].set_xlabel('Post-synaptic Neuron')
        axes[0].set_ylabel('Pre-synaptic Neuron')
        plt.colorbar(im1, ax=axes[0])
        
        # Plot weights if provided
        if weights is not None:
            weight_matrix = weights.cpu().numpy() * connections
            im2 = axes[1].imshow(weight_matrix, cmap='RdBu_r', aspect='equal', 
                               vmin=-np.abs(weight_matrix).max(), vmax=np.abs(weight_matrix).max())
            axes[1].set_title('Synaptic Weights')
            axes[1].set_xlabel('Post-synaptic Neuron')
            axes[1].set_ylabel('Pre-synaptic Neuron')
            plt.colorbar(im2, ax=axes[1])
            
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_network_graph(
        self,
        connection_matrix: torch.Tensor,
        weights: Optional[torch.Tensor] = None,
        neuron_positions: Optional[Dict[int, Tuple[float, float]]] = None,
        title: str = "Network Graph",
        save_path: Optional[str] = None
    ):
        """
        Create network graph visualization
        
        Args:
            connection_matrix: Binary connectivity matrix
            weights: Optional weight matrix
            neuron_positions: Optional fixed positions for neurons
            title: Plot title
            save_path: Path to save figure
        """
        connections = connection_matrix.cpu().numpy()
        num_neurons = connections.shape[0]
        
        # Create NetworkX graph
        G = nx.DiGraph()
        G.add_nodes_from(range(num_neurons))
        
        # Add edges
        for i in range(num_neurons):
            for j in range(num_neurons):
                if connections[i, j]:
                    weight = weights[i, j].item() if weights is not None else 1.0
                    G.add_edge(i, j, weight=weight)
                    
        # Set positions
        if neuron_positions is None:
            pos = nx.spring_layout(G, k=1, iterations=50)
        else:
            pos = neuron_positions
            
        # Create plot
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, node_color='lightblue', 
                              node_size=300, alpha=0.8, ax=ax)
        
        # Draw edges with weights
        if weights is not None:
            edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
            edge_colors = ['red' if w < 0 else 'blue' for w in edge_weights]
            edge_widths = [abs(w) * 2 for w in edge_weights]
        else:
            edge_colors = 'gray'
            edge_widths = 1
            
        nx.draw_networkx_edges(G, pos, edge_color=edge_colors, 
                              width=edge_widths, alpha=0.6, ax=ax)
        
        # Draw labels
        nx.draw_networkx_labels(G, pos, font_size=8, ax=ax)
        
        ax.set_title(title)
        ax.axis('off')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_learning_dynamics(
        self,
        spa_stats: Dict[str, Any],
        stdp_stats: Dict[str, Any],
        title: str = "Learning Dynamics",
        save_path: Optional[str] = None
    ):
        """
        Plot learning dynamics over time
        
        Args:
            spa_stats: SPA algorithm statistics
            stdp_stats: STDP learning statistics
            title: Plot title
            save_path: Path to save figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # SPA connectivity evolution
        if 'connection_history' in spa_stats and spa_stats['connection_history']:
            history = spa_stats['connection_history']
            steps = [h['step'] for h in history]
            connectivity = [h['connectivity'] for h in history]
            num_connections = [h['num_connections'] for h in history]
            
            axes[0, 0].plot(steps, connectivity, 'b-', label='Connectivity Ratio')
            axes[0, 0].set_xlabel('Time Step')
            axes[0, 0].set_ylabel('Connectivity Ratio')
            axes[0, 0].set_title('SPA Connectivity Evolution')
            axes[0, 0].grid(True, alpha=0.3)
            
            ax_twin = axes[0, 0].twinx()
            ax_twin.plot(steps, num_connections, 'r--', label='Number of Connections')
            ax_twin.set_ylabel('Number of Connections', color='r')
            
        # STDP events
        axes[0, 1].bar(['LTP Events', 'LTD Events'], 
                      [stdp_stats.get('ltp_events', 0), stdp_stats.get('ltd_events', 0)],
                      color=['green', 'red'], alpha=0.7)
        axes[0, 1].set_title('STDP Learning Events')
        axes[0, 1].set_ylabel('Number of Events')
        
        # Weight changes over time
        if 'recent_changes' in stdp_stats and stdp_stats['recent_changes']:
            changes = stdp_stats['recent_changes']
            ltp_changes = [c['ltp'] for c in changes]
            ltd_changes = [c['ltd'] for c in changes]
            
            axes[1, 0].plot(ltp_changes, 'g-', label='LTP', alpha=0.7)
            axes[1, 0].plot(ltd_changes, 'r-', label='LTD', alpha=0.7)
            axes[1, 0].set_xlabel('Update Step')
            axes[1, 0].set_ylabel('Number of Changes')
            axes[1, 0].set_title('Recent STDP Changes')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            
        # SPA statistics summary
        spa_summary = [
            spa_stats.get('pruning_events', 0),
            spa_stats.get('growth_events', 0),
            spa_stats.get('num_connections', 0)
        ]
        axes[1, 1].bar(['Pruning', 'Growth', 'Total Connections'], spa_summary,
                      color=['orange', 'purple', 'blue'], alpha=0.7)
        axes[1, 1].set_title('SPA Summary Statistics')
        axes[1, 1].set_ylabel('Count')
        
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_memory_dynamics(
        self,
        memory_stats: Dict[str, Any],
        memory_activity: List[Dict[str, Any]],
        title: str = "Memory Network Dynamics",
        save_path: Optional[str] = None
    ):
        """
        Visualize memory network dynamics
        
        Args:
            memory_stats: Memory network statistics
            memory_activity: Time series of memory activity
            title: Plot title
            save_path: Path to save figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Memory loop states
        if 'loop_states' in memory_stats:
            loop_states = memory_stats['loop_states']
            loop_ids = list(loop_states.keys())
            activities = [loop_states[lid]['activity'] for lid in loop_ids]
            is_active = [loop_states[lid]['is_active'] for lid in loop_ids]
            
            colors = ['green' if active else 'red' for active in is_active]
            axes[0, 0].bar(range(len(loop_ids)), activities, color=colors, alpha=0.7)
            axes[0, 0].set_xlabel('Memory Loop')
            axes[0, 0].set_ylabel('Activity Level')
            axes[0, 0].set_title('Memory Loop Activities')
            axes[0, 0].set_xticks(range(len(loop_ids)))
            axes[0, 0].set_xticklabels(loop_ids, rotation=45)
            
        # Memory activity over time
        if memory_activity:
            times = [m['time'] for m in memory_activity]
            active_loops = [m['active_loops'] for m in memory_activity]
            global_activity = [m['global_activity'] for m in memory_activity]
            
            axes[0, 1].plot(times, active_loops, 'b-', label='Active Loops')
            axes[0, 1].set_xlabel('Time')
            axes[0, 1].set_ylabel('Number of Active Loops')
            axes[0, 1].set_title('Memory Activity Over Time')
            axes[0, 1].grid(True, alpha=0.3)
            
            ax_twin = axes[0, 1].twinx()
            ax_twin.plot(times, global_activity, 'r--', label='Global Activity')
            ax_twin.set_ylabel('Global Activity Level', color='r')
            
        # Memory patterns
        stored_patterns = memory_stats.get('stored_patterns', [])
        axes[1, 0].bar(['Stored Patterns'], [len(stored_patterns)], color='purple', alpha=0.7)
        axes[1, 0].set_title('Stored Memory Patterns')
        axes[1, 0].set_ylabel('Count')
        
        # Loop connectivity visualization
        if 'loop_states' in memory_stats:
            loop_data = []
            for loop_id, state in memory_stats['loop_states'].items():
                loop_data.append({
                    'id': loop_id,
                    'neurons': len(state.get('neurons', [])),
                    'activity': state['activity'],
                    'activations': state.get('activation_count', 0)
                })
                
            if loop_data:
                neurons_per_loop = [ld['neurons'] for ld in loop_data]
                axes[1, 1].pie(neurons_per_loop, labels=[ld['id'] for ld in loop_data],
                              autopct='%1.1f%%', startangle=90)
                axes[1, 1].set_title('Neuron Distribution Across Loops')
                
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
